<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>نظام المكافآت والخصومات</title>
  <link rel="stylesheet" href="style.css" />
  <link rel="stylesheet" href="rewards-deductions.css">
  <link rel="stylesheet" href="pagination-styles.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="no-sidebar-layout.css">
  <script src="permissions.js" defer></script>
</head>
<body class="rewards-deductions-page">

    <!-- زر العودة للوحة التحكم -->
  <div class="back-to-dashboard">
    <a href="rewards-deductions-cards.html" class="dashboard-btn">
      <i class="fas fa-arrow-right"></i>
      <span>العودة لإدارة المكافآت والخصومات</span>
    </a>
  </div>





  <div class="main-content full-width" id="mainContent">
    <h1>نظام المكافآت والخصومات</h1>



    <!-- تبويب إضافة مكافأة -->
    <div class="tab-content" id="add-reward" style="display: none;">
      <div class="reward-form">
        <div class="form-group">
          <label for="employeeSearchReward">البحث عن الموظف (بالاسم أو الكود):</label>
          <input type="text" id="employeeSearchReward" class="employee-search-input" placeholder="أدخل اسم أو كود الموظف" list="employeeSearchSuggestionsReward" autocomplete="off">
          <datalist id="employeeSearchSuggestionsReward"></datalist>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="employeeCodeReward">كود الموظف:</label>
            <input type="text" id="employeeCodeReward" placeholder="كود الموظف" readonly>
          </div>

          <div class="form-group">
            <label for="employeeNameReward">اسم الموظف:</label>
            <input type="text" id="employeeNameReward" placeholder="اسم الموظف" readonly>
          </div>

          <div class="form-group">
            <label for="employeeDepartmentReward">الإدارة:</label>
            <input type="text" id="employeeDepartmentReward" placeholder="الإدارة" readonly>
          </div>

          <div class="form-group">
            <label for="rewardAmount">المكافأة:</label>
            <input type="number" id="rewardAmount" placeholder="مبلغ المكافأة" min="0" step="1">
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="rewardReason">سبب المكافأة:</label>
            <input type="text" id="rewardReason" placeholder="سبب المكافأة">
          </div>

          <div class="form-group">
            <label for="rewardDate">تاريخ المكافأة:</label>
            <input type="date" id="rewardDate">
          </div>

          <div class="form-group">
            <label>&nbsp;</label>
            <div></div>
          </div>

          <div class="form-group">
            <label>&nbsp;</label>
            <div></div>
          </div>
        </div>

        <div class="form-group">
          <label for="rewardNotes">ملاحظات (اختياري):</label>
          <textarea id="rewardNotes" placeholder="ملاحظات إضافية" rows="3"></textarea>
        </div>

        <div class="form-actions">
          <button id="saveReward" class="save-btn">حفظ المكافأة</button>
          <button id="resetRewardForm" class="reset-btn">إعادة تعيين</button>
        </div>
      </div>

      <!-- قسم عرض المكافآت -->
      <div class="view-section" data-permission="view_rewards_list">
        <h2>عرض المكافآت</h2>

        <!-- فلاتر البحث المحددة -->
        <div class="filtered-search-container">
          <div class="search-filters-row">
            <div class="form-group">
              <label for="filterRewardEmployeeCode">كود الموظف:</label>
              <input type="text" id="filterRewardEmployeeCode" placeholder="أدخل كود الموظف" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterRewardEmployeeName">اسم الموظف:</label>
              <input type="text" id="filterRewardEmployeeName" placeholder="أدخل اسم الموظف" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterRewardFromDate">من تاريخ:</label>
              <input type="date" id="filterRewardFromDate" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterRewardToDate">إلى تاريخ:</label>
              <input type="date" id="filterRewardToDate" class="filter-input">
            </div>
          </div>

          <div class="filter-actions">
            <button id="clearRewardFiltersBtn" class="reset-btn">مسح الفلاتر</button>
            <button id="exportRewardsBtn" class="export-btn" data-permission="export_rewards">تصدير إلى Excel</button>
          </div>
        </div>

        <!-- جدول المكافآت -->
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th>الكود</th>
                <th>اسم الموظف</th>
                <th>الإدارة</th>
                <th>المكافأة</th>
                <th>سبب المكافأة</th>
                <th>تاريخ المكافأة</th>
                <th>ملاحظات</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody id="rewardsTableBody">
              <!-- سيتم ملء البيانات ديناميكيًا -->
            </tbody>
          </table>

          <!-- نظام التحكم في الصفحات للمكافآت -->
          <div class="pagination-container" id="paginationContainerRewards">
            <button class="pagination-btn prev-btn" id="prevBtnRewards">
              <i class="fas fa-chevron-right"></i> السابق
            </button>

            <div class="page-numbers" id="pageNumbersRewards">
              <!-- سيتم إضافة أرقام الصفحات ديناميكياً -->
            </div>

            <button class="pagination-btn next-btn" id="nextBtnRewards">
              التالي <i class="fas fa-chevron-left"></i>
            </button>

            <div class="page-info" id="pageInfoRewards">
              <!-- سيتم عرض معلومات الصفحة هنا -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- تبويب إضافة خصم -->
    <div class="tab-content" id="add-deduction" style="display: none;">
      <div class="deduction-form">
        <div class="form-group">
          <label for="employeeSearchDeduction">البحث عن الموظف (بالاسم أو الكود):</label>
          <input type="text" id="employeeSearchDeduction" class="employee-search-input" placeholder="أدخل اسم أو كود الموظف" list="employeeSearchSuggestionsDeduction" autocomplete="off">
          <datalist id="employeeSearchSuggestionsDeduction"></datalist>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="employeeCodeDeduction">كود الموظف:</label>
            <input type="text" id="employeeCodeDeduction" placeholder="كود الموظف" readonly>
          </div>

          <div class="form-group">
            <label for="employeeNameDeduction">اسم الموظف:</label>
            <input type="text" id="employeeNameDeduction" placeholder="اسم الموظف" readonly>
          </div>

          <div class="form-group">
            <label for="employeeDepartmentDeduction">الإدارة:</label>
            <input type="text" id="employeeDepartmentDeduction" placeholder="الإدارة" readonly>
          </div>

          <div class="form-group">
            <label for="deductionAmount">الخصم:</label>
            <input type="number" id="deductionAmount" placeholder="مبلغ الخصم" min="0" step="1">
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="deductionReason">سبب الخصم:</label>
            <input type="text" id="deductionReason" placeholder="سبب الخصم">
          </div>

          <div class="form-group">
            <label for="deductionDate">تاريخ الخصم:</label>
            <input type="date" id="deductionDate">
          </div>

          <div class="form-group">
            <label>&nbsp;</label>
            <div></div>
          </div>

          <div class="form-group">
            <label>&nbsp;</label>
            <div></div>
          </div>
        </div>

        <div class="form-group">
          <label for="deductionNotes">ملاحظات (اختياري):</label>
          <textarea id="deductionNotes" placeholder="ملاحظات إضافية" rows="3"></textarea>
        </div>

        <div class="form-actions">
          <button id="saveDeduction" class="save-btn">حفظ الخصم</button>
          <button id="resetDeductionForm" class="reset-btn">إعادة تعيين</button>
        </div>
      </div>

      <!-- قسم عرض الخصومات -->
      <div class="view-section" data-permission="view_deductions_list">
        <h2>عرض الخصومات</h2>

        <!-- فلاتر البحث المحددة -->
        <div class="filtered-search-container">
          <div class="search-filters-row">
            <div class="form-group">
              <label for="filterDeductionEmployeeCode">كود الموظف:</label>
              <input type="text" id="filterDeductionEmployeeCode" placeholder="أدخل كود الموظف" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterDeductionEmployeeName">اسم الموظف:</label>
              <input type="text" id="filterDeductionEmployeeName" placeholder="أدخل اسم الموظف" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterDeductionFromDate">من تاريخ:</label>
              <input type="date" id="filterDeductionFromDate" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterDeductionToDate">إلى تاريخ:</label>
              <input type="date" id="filterDeductionToDate" class="filter-input">
            </div>
          </div>

          <div class="filter-actions">
            <button id="clearDeductionFiltersBtn" class="reset-btn">مسح الفلاتر</button>
            <button id="exportDeductionsBtn" class="export-btn" data-permission="export_deductions">تصدير إلى Excel</button>
          </div>
        </div>

        <!-- جدول الخصومات -->
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th>الكود</th>
                <th>اسم الموظف</th>
                <th>الإدارة</th>
                <th>الخصم</th>
                <th>سبب الخصم</th>
                <th>تاريخ الخصم</th>
                <th>ملاحظات</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody id="deductionsTableBody">
              <!-- سيتم ملء البيانات ديناميكيًا -->
            </tbody>
          </table>

          <!-- نظام التحكم في الصفحات للخصومات -->
          <div class="pagination-container" id="paginationContainerDeductions">
            <button class="pagination-btn prev-btn" id="prevBtnDeductions">
              <i class="fas fa-chevron-right"></i> السابق
            </button>

            <div class="page-numbers" id="pageNumbersDeductions">
              <!-- سيتم إضافة أرقام الصفحات ديناميكياً -->
            </div>

            <button class="pagination-btn next-btn" id="nextBtnDeductions">
              التالي <i class="fas fa-chevron-left"></i>
            </button>

            <div class="page-info" id="pageInfoDeductions">
              <!-- سيتم عرض معلومات الصفحة هنا -->
            </div>
          </div>
        </div>
      </div>
    </div>



    <!-- تبويب التقارير -->
    <div class="tab-content" id="reports" style="display: none;" data-permission="view_rewards_deductions_reports">
      <!-- فلاتر التقارير -->
      <div class="reports-filters">
        <div class="filter-group">
          <label for="reportEmployeeSearch">البحث عن الموظف (بالاسم أو الكود):</label>
          <input type="text" id="reportEmployeeSearch" placeholder="أدخل اسم أو كود الموظف" list="reportEmployeeSuggestions" autocomplete="off">
          <datalist id="reportEmployeeSuggestions"></datalist>
        </div>

        <div class="filter-group">
          <label for="reportDepartmentFilter">الإدارة:</label>
          <select id="reportDepartmentFilter">
            <option value="">كل الإدارات</option>
          </select>
        </div>

        <div class="filter-group">
          <label for="reportStartDate">من تاريخ:</label>
          <input type="date" id="reportStartDate">
        </div>

        <div class="filter-group">
          <label for="reportEndDate">إلى تاريخ:</label>
          <input type="date" id="reportEndDate">
        </div>

        <div class="filter-actions">
          <button id="applyReportFilters" class="search-btn">تطبيق الفلاتر</button>
          <button id="clearReportFilters" class="reset-btn">مسح الفلاتر</button>
          <div id="reportsLoading" class="loading" style="display: none;">جاري تحديث التقارير...</div>
        </div>
      </div>

      <!-- البطاقات الإحصائية -->
      <div class="stats-cards">
        <div class="stat-card rewards-card">
          <div class="stat-icon">
            <i class="fas fa-gift"></i>
          </div>
          <div class="stat-content">
            <h3>إجمالي المكافآت</h3>
            <div class="stat-value" id="totalRewards">0</div>
            <div class="stat-count" id="rewardsCount">0 مكافأة</div>
            <button class="details-btn" onclick="showRewardsDetails()">تفاصيل</button>
          </div>
        </div>

        <div class="stat-card deductions-card">
          <div class="stat-icon">
            <i class="fas fa-minus-circle"></i>
          </div>
          <div class="stat-content">
            <h3>إجمالي الخصومات</h3>
            <div class="stat-value" id="totalDeductions">0</div>
            <div class="stat-count" id="deductionsCount">0 خصم</div>
            <button class="details-btn" onclick="showDeductionsDetails()">تفاصيل</button>
          </div>
        </div>

        <div class="stat-card total-card">
          <div class="stat-icon">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="stat-content">
            <h3>إجمالي المكافآت والخصومات</h3>
            <div class="stat-value" id="totalRewardsAndDeductions">0</div>
            <div class="stat-count" id="totalCount">0 عملية</div>
            <button class="details-btn" onclick="showAllDetails()">تفاصيل</button>
          </div>
        </div>

        <div class="stat-card net-card">
          <div class="stat-icon">
            <i class="fas fa-calculator"></i>
          </div>
          <div class="stat-content">
            <h3>الصافي</h3>
            <div class="stat-value" id="netAmount">0</div>
            <div class="stat-description">المكافآت - الخصومات</div>
            <button class="details-btn" onclick="showNetDetails()">تفاصيل</button>
          </div>
        </div>
      </div>



      <!-- الجداول القابلة للإظهار والإخفاء -->
      <div class="collapsible-tables">
        <!-- جدول أعلى المكافآت -->
        <div class="table-section">
          <div class="table-header" onclick="toggleTable('topRewardsTable')">
            <h3><i class="fas fa-trophy"></i> أعلى الموظفين حصولاً على مكافآت</h3>
            <i class="fas fa-chevron-down toggle-icon" id="topRewardsToggle"></i>
          </div>
          <div class="table-content" id="topRewardsTable" style="display: none;">
            <div class="table-actions">
              <button class="export-btn" onclick="exportTopRewards()">تصدير إلى Excel</button>
            </div>
            <div class="table-container">
              <table class="data-table">
                <thead>
                  <tr>
                    <th>الترتيب</th>
                    <th>الكود</th>
                    <th>اسم الموظف</th>
                    <th>الإدارة</th>
                    <th>عدد المكافآت</th>
                    <th>إجمالي المكافآت</th>
                  </tr>
                </thead>
                <tbody id="topRewardsTableBody">
                  <!-- سيتم ملء البيانات ديناميكيًا -->
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- جدول أعلى الخصومات -->
        <div class="table-section">
          <div class="table-header" onclick="toggleTable('topDeductionsTable')">
            <h3><i class="fas fa-exclamation-triangle"></i> أعلى الموظفين تعرضاً للخصومات</h3>
            <i class="fas fa-chevron-down toggle-icon" id="topDeductionsToggle"></i>
          </div>
          <div class="table-content" id="topDeductionsTable" style="display: none;">
            <div class="table-actions">
              <button class="export-btn" onclick="exportTopDeductions()">تصدير إلى Excel</button>
            </div>
            <div class="table-container">
              <table class="data-table">
                <thead>
                  <tr>
                    <th>الترتيب</th>
                    <th>الكود</th>
                    <th>اسم الموظف</th>
                    <th>الإدارة</th>
                    <th>عدد الخصومات</th>
                    <th>إجمالي الخصومات</th>
                  </tr>
                </thead>
                <tbody id="topDeductionsTableBody">
                  <!-- سيتم ملء البيانات ديناميكيًا -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal للتعديل -->
  <div id="editModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2 id="editModalTitle">تعديل</h2>
        <span class="close">&times;</span>
      </div>
      <div class="modal-body">
        <form id="editForm">
          <input type="hidden" id="editId">
          <input type="hidden" id="editType">

          <!-- الصف الأول: كود الموظف، اسم الموظف، الإدارة -->
          <div class="form-row">
            <div class="form-group">
              <label for="editEmployeeCode">كود الموظف:</label>
              <input type="text" id="editEmployeeCode" readonly>
            </div>

            <div class="form-group">
              <label for="editEmployeeName">اسم الموظف:</label>
              <input type="text" id="editEmployeeName" readonly>
            </div>

            <div class="form-group">
              <label for="editEmployeeDepartment">الإدارة:</label>
              <input type="text" id="editEmployeeDepartment" readonly>
            </div>
          </div>

          <!-- الصف الثاني: المبلغ، السبب، التاريخ -->
          <div class="form-row">
            <div class="form-group">
              <label for="editAmount" id="editAmountLabel">المبلغ:</label>
              <input type="number" id="editAmount" min="0" step="1">
            </div>

            <div class="form-group">
              <label for="editReason" id="editReasonLabel">السبب:</label>
              <input type="text" id="editReason">
            </div>

            <div class="form-group">
              <label for="editDate" id="editDateLabel">التاريخ:</label>
              <input type="date" id="editDate">
            </div>
          </div>

          <!-- الصف الثالث: الملاحظات -->
          <div class="form-row">
            <div class="form-group full-width">
              <label for="editNotes">ملاحظات:</label>
              <textarea id="editNotes" rows="3"></textarea>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button id="saveEdit" class="save-btn">حفظ التعديل</button>
        <button class="cancel-btn">إلغاء</button>
      </div>
    </div>
  </div>

  <!-- Modal لتفاصيل التقارير -->
  <div id="detailsModal" class="modal">
    <div class="modal-content large-modal">
      <div class="modal-header">
        <h2 id="detailsModalTitle">تفاصيل التقرير</h2>
        <span class="close" onclick="closeDetailsModal()">&times;</span>
      </div>
      <div class="modal-body">
        <div class="details-summary">
          <div class="summary-item">
            <span class="summary-label">إجمالي العدد:</span>
            <span class="summary-value" id="detailsTotalCount">0</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">إجمالي المبلغ:</span>
            <span class="summary-value" id="detailsTotalAmount">0</span>
          </div>
        </div>
        <div class="details-actions">
          <button class="export-btn" onclick="exportDetailsData()">تصدير إلى Excel</button>
        </div>
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th>الكود</th>
                <th>اسم الموظف</th>
                <th>الإدارة</th>
                <th>النوع</th>
                <th>المبلغ</th>
                <th>السبب</th>
                <th>التاريخ</th>
                <th>ملاحظات</th>
              </tr>
            </thead>
            <tbody id="detailsTableBody">
              <!-- سيتم ملء البيانات ديناميكيًا -->
            </tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button class="cancel-btn" onclick="closeDetailsModal()">إغلاق</button>
      </div>
    </div>
  </div>

  <script src="shared-utils.js"></script>
  <script src="dateUtils.js"></script>
  <script src="arabic-date-picker.js"></script>
  <script src="form-validation.js"></script>
  <script src="permissions.js"></script>
  <script src="main.js"></script>
  <script src="rewards-deductions.js?v=24"></script>
  <script>
    // تهيئة main.js بعد تحميل permissions.js
    
    };
    document.head.appendChild(script);
});
</script>
</body>
</html>