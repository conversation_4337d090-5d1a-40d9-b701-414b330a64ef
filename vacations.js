console.log('بدء تحميل ملف vacations.js');

// دالة لتحديد ما إذا كانت الإجازة قبل 26-6 (العام المالي السابق)
function isBeforeJune26(vacationDate) {
  const vacDate = new Date(vacationDate);
  const currentYear = new Date().getFullYear();

  // تحديد تاريخ 26-6 للسنة الحالية
  const june26ThisYear = new Date(currentYear, 5, 26); // الشهر 5 = يونيو (0-based)

  // إذا كان تاريخ الإجازة قبل 26-6 من السنة الحالية
  return vacDate < june26ThisYear;
}

// دالة تعيين التواريخ الافتراضية (من 26-6 إلى 25-6)
function setDefaultDateRange() {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();

  // تحديد سنة الإجازة (من 26 يونيو إلى 25 يونيو)
  let vacationYear = currentYear;
  if (currentDate.getMonth() < 5 || (currentDate.getMonth() === 5 && currentDate.getDate() < 26)) {
    // إذا كنا قبل 26 يونيو، فنحن في سنة الإجازة السابقة
    vacationYear = currentYear - 1;
  }

  // تعيين التواريخ الافتراضية
  const startDate = `${vacationYear}-06-26`;
  const endDate = `${vacationYear + 1}-06-25`;

  // لا نعيّن التواريخ في حقول فلتر نافذة التفاصيل لتمكين البحث في جميع التواريخ
  // const filterFromDate = document.getElementById('filterVacationFromDate');
  // const filterToDate = document.getElementById('filterVacationToDate');

  // if (filterFromDate) {
  //   filterFromDate.value = startDate;
  // }

  // if (filterToDate) {
  //   filterToDate.value = endDate;
  // }

  // تعيين التواريخ في حقول التقارير العامة
  const reportStartDate = document.getElementById('startDate');
  const reportEndDate = document.getElementById('endDate');

  if (reportStartDate) {
    reportStartDate.value = startDate;
  }

  if (reportEndDate) {
    reportEndDate.value = endDate;
  }

  // تعيين التواريخ في تقرير إجمالي الموظفين
  const empSummaryStartDate = document.getElementById('employeeSummaryStartDate');
  const empSummaryEndDate = document.getElementById('employeeSummaryEndDate');

  if (empSummaryStartDate) {
    empSummaryStartDate.value = startDate;
  }

  if (empSummaryEndDate) {
    empSummaryEndDate.value = endDate;
  }

  // تعيين التواريخ في تقرير إجمالي الأقسام
  const deptSummaryStartDate = document.getElementById('deptSummaryStartDate');
  const deptSummaryEndDate = document.getElementById('deptSummaryEndDate');

  if (deptSummaryStartDate) {
    deptSummaryStartDate.value = startDate;
  }

  if (deptSummaryEndDate) {
    deptSummaryEndDate.value = endDate;
  }

  // تعيين التواريخ في تقرير الغياب
  const absenceStartDate = document.getElementById('absenceStartDate');
  const absenceEndDate = document.getElementById('absenceEndDate');

  if (absenceStartDate) {
    absenceStartDate.value = startDate;
  }

  if (absenceEndDate) {
    absenceEndDate.value = endDate;
  }

  // تعيين التواريخ في تقرير أيام الغياب
  const absenceDaysStartDate = document.getElementById('absenceDaysStartDate');
  const absenceDaysEndDate = document.getElementById('absenceDaysEndDate');

  if (absenceDaysStartDate) {
    absenceDaysStartDate.value = startDate;
  }

  if (absenceDaysEndDate) {
    absenceDaysEndDate.value = endDate;
  }

  // تعيين التواريخ في تقرير الموظفين الأعلى استخداماً
  const topUsersStartDate = document.getElementById('topUsersStartDate');
  const topUsersEndDate = document.getElementById('topUsersEndDate');

  if (topUsersStartDate) {
    topUsersStartDate.value = startDate;
  }

  if (topUsersEndDate) {
    topUsersEndDate.value = endDate;
  }

  // تعيين التواريخ في تقرير الموظف المخصص
  const customEmployeeStartDate = document.getElementById('customEmployeeStartDate');
  const customEmployeeEndDate = document.getElementById('customEmployeeEndDate');

  if (customEmployeeStartDate) {
    customEmployeeStartDate.value = startDate;
  }

  if (customEmployeeEndDate) {
    customEmployeeEndDate.value = endDate;
  }
}

document.addEventListener("DOMContentLoaded", () => {
  console.log('تم تحميل صفحة الإجازات - DOMContentLoaded');

  // تحقق سريع من وجود التوكن
  const quickToken = localStorage.getItem('token');
  if (!quickToken) {
    alert('لا يوجد توكن مصادقة. سيتم توجيهك لصفحة تسجيل الدخول.');
    window.location.href = 'login.html';
    return;
  }

  // التحقق من تحميل DateUtils
  if (typeof DateUtils === 'undefined') {
    console.warn('DateUtils غير محمل، قد تحدث مشاكل في عرض التواريخ');
  }

  // المتغيرات العامة
  let API_URL = localStorage.getItem('serverUrl') || "http://localhost:5500";

  // التأكد من أن API_URL لا يحتوي على /api في النهاية
  if (API_URL.endsWith('/api')) {
    API_URL = API_URL.slice(0, -4);
  }
  let departments = [];
  let employees = [];
  let vacations = [];
  let addedVacations = []; // مصفوفة لتخزين الإجازات المضافة في الجلسة الحالية

  // متغيرات نظام الصفحات للإجازات المضافة
  let currentPageAddedVacations = 1;
  const itemsPerPageAddedVacations = 50;
  let totalAddedVacations = 0;

  // متغيرات نظام الصفحات لعرض الإجازات
  let currentPageVacations = 1;
  const itemsPerPageVacations = 50;
  let totalVacations = 0;
  
  // عناصر DOM
  const employeeSearchAdd = document.getElementById('employeeSearchAdd');
  const employeeCode = document.getElementById('employeeCode');
  const employeeName = document.getElementById('employeeName');
  const employeeDepartment = document.getElementById('employeeDepartment');
  const vacationType = document.getElementById('vacationType');
  const officialHolidayGroup = document.getElementById('officialHolidayGroup');
  const officialHoliday = document.getElementById('officialHoliday');
  const vacationDate = document.getElementById('vacationDate');
  const daysCountGroup = document.getElementById('daysCountGroup');
  const daysCount = document.getElementById('daysCount');
  const saveVacationBtn = document.getElementById('saveVacation');
  const resetFormBtn = document.getElementById('resetForm');
  const tabBtns = document.querySelectorAll('.tab-btn');
  const tabContents = document.querySelectorAll('.tab-content');
  const departmentFilterView = document.getElementById('departmentFilterView');
  const startDateFilter = document.getElementById('startDate');
  const endDateFilter = document.getElementById('endDate');
  const searchVacationsBtn = document.getElementById('searchVacationsBtn');
  const resetVacationsBtn = document.getElementById('resetVacationsBtn');
  const exportVacationsBtn = document.getElementById('exportVacationsBtn');
  const vacationsTableBody = document.getElementById('vacationsTableBody');
  const vacationDetailsModal = document.getElementById('vacationDetailsModal');
  const vacationDetailsTableBody = document.getElementById('vacationDetailsTableBody');
  const addedVacationsTableBody = document.getElementById('addedVacationsTableBody'); // جدول الإجازات المضافة
  
  // دالة مساعدة لتنسيق التاريخ بشكل آمن (تجنب مشكلة نقص اليوم)
  function formatDateSafe(dateString) {
    if (!dateString) return 'غير محدد';

    if (typeof DateUtils !== 'undefined') {
      return DateUtils.formatDateFromDatabase(dateString);
    }

    // حل بديل إذا لم تكن DateUtils متاحة - استخدام نفس صيغة المساهمات
    if (typeof dateString === 'string' && /^\d{4}-\d{2}-\d{2}/.test(dateString)) {
      const [year, month, day] = dateString.split('-');
      return `${day}/${month}/${year}`;
    }

    // للحالات الأخرى، استخدم الطريقة العادية
    try {
      const date = new Date(dateString);
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    } catch (error) {
      console.error('خطأ في تنسيق التاريخ:', error);
      return 'تاريخ غير صحيح';
    }
  }

  // تحميل البيانات
  async function loadData() {
    try {
      // التحقق من وجود التوكن
      const token = localStorage.getItem('token');
      console.log('التحقق من التوكن:', token ? 'موجود' : 'غير موجود');
      if (!token) {
        console.log('لا يوجد توكن، إعادة توجيه لصفحة تسجيل الدخول');
        window.location.href = 'login.html';
        return;
      }

      console.log(`محاولة تحميل البيانات من: ${API_URL}`);
      console.log('التوكن المتاح:', token ? 'موجود' : 'غير موجود');
      
      try {
        await loadDepartments();
        console.log('تم تحميل الإدارات بنجاح');
      } catch (deptError) {
        console.error('فشل في تحميل الإدارات:', deptError);
        alert('فشل في تحميل الإدارات: ' + deptError.message);
      }
      
      try {
        await loadEmployees();
        console.log('تم تحميل الموظفين بنجاح');
      } catch (empError) {
        console.error('فشل في تحميل الموظفين:', empError);
        alert('فشل في تحميل الموظفين: ' + empError.message);
      }

      // تعيين التواريخ الافتراضية بعد تحميل البيانات
      setDefaultDateRange();
      
      try {
        await loadVacations();
        console.log('تم تحميل الإجازات بنجاح');
        // إخفاء البيانات افتراضياً عند تحميل الصفحة
        vacationsTableBody.innerHTML = '<tr><td colspan="10" class="no-results">يرجى الضغط على زر "بحث" أو "عرض الكل" لعرض البيانات</td></tr>';
      } catch (vacError) {
        console.error('فشل في تحميل الإجازات:', vacError);
        alert('فشل في تحميل الإجازات: ' + vacError.message);
      }

      return true;
    } catch (error) {
      console.error('فشل في تحميل البيانات:', error);
      alert('فشل في تحميل البيانات: ' + error.message);
      return false;
    }
  }

  // مسح البيانات
  function clearData() {
    departments = [];
    employees = [];
    vacations = [];
    departmentFilterView.innerHTML = '<option value="">كل الإدارات</option>';
    vacationsTableBody.innerHTML = '<tr><td colspan="10" class="no-results">يرجى الضغط على زر "بحث" أو "عرض الكل" لعرض البيانات</td></tr>';
  }

  // تحميل الإدارات باستخدام النظام الموحد
  async function loadDepartments() {
    try {
      if (window.SharedUtils && window.SharedUtils.loadDepartments) {
        departments = await window.SharedUtils.loadDepartments();
        console.log('تم تحميل الإدارات بنجاح من النظام الموحد:', departments);
        populateDepartmentSelects();
      } else {
        // fallback للكود القديم
        console.log(`جاري تحميل الإدارات من: ${API_URL}/api/departments`);
        const response = await fetch(`${API_URL}/api/departments`);
        console.log('استجابة تحميل الإدارات:', response.status);

        if (response.ok) {
          departments = await response.json();
          console.log('تم تحميل الإدارات بنجاح:', departments);
          populateDepartmentSelects();
        } else {
          console.error(`فشل في تحميل الإدارات: ${response.status} ${response.statusText}`);
          throw new Error(`فشل في تحميل الإدارات: ${response.status} ${response.statusText}`);
        }
      }
    } catch (error) {
      console.error('خطأ في تحميل الإدارات:', error);
      throw error; // إعادة رمي الخطأ للتعامل معه في المستدعي
    }
  }

  // تعبئة قوائم الإدارات
  function populateDepartmentSelects() {
    // تعبئة قائمة الإدارات في فلتر العرض
    departmentFilterView.innerHTML = '<option value="">كل الإدارات</option>';
    departments.forEach(dept => {
      const option = document.createElement('option');
      option.value = dept;
      option.textContent = dept;
      departmentFilterView.appendChild(option);
    });

    // تحديث قوائم الإدارات في التقارير أيضاً
    populateReportDepartments();
  }

  // البحث عن الموظف
  function handleEmployeeSearch(searchTerm, datalistId) {
    const datalist = document.getElementById(datalistId);
    datalist.innerHTML = '';
    

    
    if (!searchTerm || searchTerm.trim() === '') {
      // مسح الحقول عند مسح البحث
      employeeCode.value = '';
      employeeName.value = '';
      employeeDepartment.value = '';
      return;
    }
    
    const searchTermLower = searchTerm.toLowerCase().trim();
    
    // البحث في الموظفين - تحسين البحث ليشمل أجزاء من الاسم
    const filteredEmployees = employees.filter(emp => {
      // التأكد من أن البيانات موجودة وتحويلها إلى نص
      const fullName = emp.full_name ? String(emp.full_name).toLowerCase() : '';
      const code = emp.code ? String(emp.code).toLowerCase() : '';
      const department = emp.department ? String(emp.department).toLowerCase() : '';
      
      // البحث في كل الحقول
      return fullName.includes(searchTermLower) || 
             code.includes(searchTermLower) || 
             department.includes(searchTermLower);
    });
    
    console.log(`نتائج البحث: ${filteredEmployees.length} موظف`);
    if (filteredEmployees.length > 0) {
      console.log('أول نتيجة:', filteredEmployees[0]);
    }
    
    // البحث عن تطابق مباشر أولاً
    const exactCodeMatch = employees.find(emp => 
      emp.code && String(emp.code).toLowerCase() === searchTermLower
    );
    const exactNameMatch = employees.find(emp => 
      emp.full_name && String(emp.full_name).toLowerCase() === searchTermLower
    );
    
    // إذا وُجد تطابق مباشر، املأ الحقول
    if (exactCodeMatch) {
      fillEmployeeFields(exactCodeMatch);
    } else if (exactNameMatch) {
      fillEmployeeFields(exactNameMatch);
    }
    // إذا كان هناك موظف واحد فقط في النتائج، املأ الحقول
    else if (filteredEmployees.length === 1) {
      fillEmployeeFields(filteredEmployees[0]);
    }
    // إذا كان البحث يبدأ بكود أو اسم موظف، املأ الحقول
    else {
      const startsWithMatch = employees.find(emp => {
        const nameLower = emp.full_name ? String(emp.full_name).toLowerCase() : '';
        const codeLower = emp.code ? String(emp.code).toLowerCase() : '';
        return nameLower.startsWith(searchTermLower) || codeLower.startsWith(searchTermLower);
      });
      
      if (startsWithMatch) {
        fillEmployeeFields(startsWithMatch);
      }
    }
    
    // إضافة الخيارات للقائمة المنسدلة - تحسين عرض الاقتراحات
    if (filteredEmployees.length > 0) {
      // ترتيب النتائج: الأكواد أولاً ثم الأسماء
      filteredEmployees.sort((a, b) => {
        // إذا كان البحث يطابق الكود، ضع هذا الموظف في المقدمة
        const aCodeMatch = a.code && String(a.code).toLowerCase().includes(searchTermLower);
        const bCodeMatch = b.code && String(b.code).toLowerCase().includes(searchTermLower);
        
        if (aCodeMatch && !bCodeMatch) return -1;
        if (!aCodeMatch && bCodeMatch) return 1;
        
        // ثم رتب حسب الاسم
        return (a.full_name || '').localeCompare(b.full_name || '');
      });
      
      // عرض أول 15 نتيجة فقط لتحسين الأداء
      filteredEmployees.slice(0, 15).forEach(emp => {
        const option = document.createElement('option');
        option.value = `${emp.code} - ${emp.full_name}`;
        datalist.appendChild(option);
      });
    } else {
      // إضافة رسالة إذا لم يتم العثور على نتائج
      const option = document.createElement('option');
      option.value = "لا توجد نتائج مطابقة";
      datalist.appendChild(option);
    }
  }

  // وظيفة مساعدة لملء حقول الموظف
  function fillEmployeeFields(employee) {
    if (employee) {
      employeeCode.value = employee.code || '';
      employeeName.value = employee.full_name || '';
      employeeDepartment.value = employee.department || '';
    }
  }

  // اختيار الموظف
  function handleEmployeeSelection(value) {
    if (!value) return;
    
    const codeMatch = value.match(/^(\S+)\s*-/);
    if (!codeMatch) return;
    
    const code = codeMatch[1];
    const employee = employees.find(emp => emp.code == code);
    
    if (employee) {
      employeeCode.value = employee.code;
      employeeName.value = employee.full_name;
      employeeDepartment.value = employee.department || '';
    }
  }

  // تحميل الإجازات
  async function loadVacations() {
    try {
      console.log(`جاري تحميل الإجازات من: ${API_URL}/api/vacations`);
      const token = localStorage.getItem('token');
      console.log('التوكن المستخدم في loadVacations:', token ? 'موجود' : 'غير موجود');
      const response = await fetch(`${API_URL}/api/vacations`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('استجابة تحميل الإجازات:', response.status);
      
      if (response.ok) {
        vacations = await response.json();
        console.log('تم تحميل الإجازات بنجاح، عدد الإجازات:', vacations.length);
        
        // إضافة الإجازات إلى مصفوفة الإجازات المضافة
        addedVacations = [];
        if (vacations && vacations.length > 0) {
          // إضافة الإجازات مباشرة من قاعدة البيانات (تحتوي على جميع البيانات المطلوبة)
          for (const vacation of vacations) {
            addedVacations.push({
              id: vacation.id,
              employee_code: vacation.employee_code,
              employee_name: vacation.employee_name,
              department: vacation.department,
              vacation_type: vacation.vacation_type,
              vacation_date: vacation.vacation_date,
              days_count: vacation.days_count || 1,
              official_type: vacation.official_type,
              created_at: vacation.created_at,
              // استخدام created_at كـ timestamp للترتيب
              added_timestamp: vacation.created_at ? new Date(vacation.created_at).getTime() : vacation.id || Date.now()
            });
          }
        }

        // عرض الإجازات المضافة فقط (لا نعرض جدول الإجازات الرئيسي تلقائياً)
        displayAddedVacations();
      } else if (response.status === 401 || response.status === 403) {
        // التوكن منتهي الصلاحية أو غير صالح
        handleTokenExpired();
        return;
      } else {
        console.error(`فشل في تحميل الإجازات: ${response.status} ${response.statusText}`);
        throw new Error(`فشل في تحميل الإجازات: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.error('خطأ في جلب بيانات الإجازات:', error);
      throw error; // إعادة رمي الخطأ للتعامل معه في المستدعي
    }
  }

  // تحميل الموظفين
  async function loadEmployees() {
    try {
      // إضافة معامل لاستبعاد الموظفين المستقيلين
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/api/employees?include_resigned=false`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        employees = await response.json();
      } else {
        throw new Error(`فشل في تحميل الموظفين: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.error('خطأ في جلب بيانات الموظفين:', error);
      throw error; // إعادة رمي الخطأ للتعامل معه في المستدعي
    }
  }

  // تطبيع النص العربي (إزالة الهمزات والتشكيل وتوحيد التاء المربوطة والمفتوحة)
  function normalizeArabicText(text) {
    if (!text) return '';
    
    // تحويل النص إلى حروف صغيرة
    let normalized = text.toLowerCase();
    
    // استبدال الهمزات المختلفة بألف عادية
    normalized = normalized.replace(/[أإآء]/g, 'ا');
    
    // استبدال التاء المربوطة بتاء مفتوحة
    normalized = normalized.replace(/[ة]/g, 'ه');
    
    // إزالة التشكيل
    normalized = normalized.replace(/[َُِّْ~ً]/g, '');
    
    return normalized;
  }

  // دالة updateEmployeesByDepartment تم حذفها لأنها لم تعد مطلوبة مع نظام البحث الجديد

  // عرض/إخفاء حقول إضافية بناءً على نوع الإجازة
  function updateVacationTypeFields() {
    const selectedType = vacationType.value;
    
    // إظهار/إخفاء قائمة الإجازات خارج الرصيد
    if (selectedType === 'official') {
      officialHolidayGroup.style.display = 'block';
      officialHoliday.required = true;
    } else {
      officialHolidayGroup.style.display = 'none';
      officialHoliday.required = false;
    }
    
    // إظهار/إخفاء حقل عدد الأيام
    if (selectedType === 'annual' || selectedType === 'unpaid' || selectedType === 'sick') {
      daysCountGroup.style.display = 'block';
    } else {
      daysCountGroup.style.display = 'block';
      daysCount.value = 1;
    }
  }

  // حفظ الإجازة
  async function saveVacation() {
    try {
      const saveBtn = document.getElementById('saveVacation');
      const editId = saveBtn.getAttribute('data-edit-id');
      
      // التحقق من صحة البيانات
      if (!editId && !employeeCode.value) {
        alert('يرجى اختيار الموظف');
        return;
      }

      if (!vacationType.value) {
        alert('يرجى اختيار نوع الإجازة');
        return;
      }

      if (vacationType.value === 'official' && !officialHoliday.value) {
        alert('يرجى اختيار نوع الإجازة');
        return;
      }

      // التحقق من تاريخ الإجازة
      if (!vacationDate || !vacationDate.value) {
        alert('يرجى تحديد تاريخ الإجازة');
        return;
      }

      // التحقق من عدد الأيام
      const daysCountValue = parseInt(daysCount.value) || 1;
      if (daysCountValue <= 0) {
        alert('عدد الأيام يجب أن يكون أكبر من صفر');
        return;
      }

      // إعداد بيانات الإجازة
      const vacationData = {
        vacation_type: vacationType.value,
        days_count: daysCountValue
      };

      // إضافة تاريخ الإجازة
      vacationData.vacation_date = vacationDate.value;

      // إضافة نوع الإجازة إذا كان النوع خارج الرصيد
      if (vacationType.value === 'official') {
        vacationData.official_type = officialHoliday.value;
      }

      if (editId) {
        // تحديث إجازة موجودة
        await updateVacation(editId, vacationData);
      } else {
        // إضافة إجازة جديدة
        // البحث عن بيانات الموظف المحدد
        const selectedEmployee = employees.find(emp => String(emp.code) === String(employeeCode.value));
        
        if (!selectedEmployee) {
          console.error('لم يتم العثور على الموظف المحدد. كود الموظف:', employeeCode.value);
          console.log('قائمة الموظفين المتاحة:', employees.map(e => ({ code: e.code, name: e.full_name })));
          alert('خطأ: لم يتم العثور على بيانات الموظف المحدد');
          return;
        }

        vacationData.employee_code = employeeCode.value;
        vacationData.employee_name = selectedEmployee.full_name;
        vacationData.department = selectedEmployee.department;

        console.log('بيانات الإجازة المرسلة:', vacationData);

        // إنشاء إجازة منفصلة لكل يوم وإرسالها للخادم
        const startDate = vacationData.vacation_date;
        const daysCount = vacationData.days_count || 1;
        const token = localStorage.getItem('token');

        let successCount = 0;
        let failedDays = [];

        if (startDate) {
          const startDateObj = new Date(startDate);
          let currentDate = new Date(startDateObj);
          let workDaysAdded = 0;

          // إرسال كل يوم كطلب منفصل
          while (workDaysAdded < daysCount) {
            if (currentDate.getDay() !== 5) { // تخطي أيام الجمعة
              const singleDayData = {
                employee_code: vacationData.employee_code,
                employee_name: vacationData.employee_name,
                department: vacationData.department,
                vacation_type: vacationData.vacation_type,
                vacation_date: currentDate.toISOString().split('T')[0],
                official_type: vacationData.official_type
              };

              try {
                // التحقق من وجود إجازة مكررة قبل الإرسال
                const duplicateCheck = checkDuplicateVacation(
                  singleDayData.employee_code,
                  singleDayData.vacation_date
                );

                if (duplicateCheck.isDuplicate) {
                  console.log(`إجازة مكررة في ${singleDayData.vacation_date}:`, duplicateCheck.vacation);

                  // إنشاء رسالة مفصلة عن الإجازة المتعارضة
                  const conflictVacation = duplicateCheck.vacation;
                  let conflictDetails = `إجازة موجودة مسبقاً`;

                  if (conflictVacation.id) {
                    conflictDetails += ` (كود: ${conflictVacation.id})`;
                  }

                  if (conflictVacation.vacation_type) {
                    const typeText = getVacationTypeText(conflictVacation.vacation_type);
                    conflictDetails += ` - نوع: ${typeText}`;
                  }

                  conflictDetails += ` - مصدر: ${duplicateCheck.source === 'local' ? 'الجلسة الحالية' : 'قاعدة البيانات'}`;

                  failedDays.push({
                    date: singleDayData.vacation_date,
                    error: conflictDetails
                  });
                  workDaysAdded++;
                  continue;
                }

                console.log(`إرسال اليوم ${workDaysAdded + 1}:`, singleDayData);
                const response = await fetch(`${API_URL}/api/vacations`, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                  },
                  body: JSON.stringify(singleDayData)
                });

                if (response.ok) {
                  const responseData = await response.json();

                  const newVacation = {
                    id: responseData.id, // كود إجازة منفصل من قاعدة البيانات
                    employee_code: singleDayData.employee_code,
                    employee_name: singleDayData.employee_name,
                    department: singleDayData.department,
                    vacation_type: singleDayData.vacation_type,
                    vacation_date: singleDayData.vacation_date,
                    official_type: singleDayData.official_type,
                    added_timestamp: Date.now() + workDaysAdded
                  };

                  addedVacations.push(newVacation);
                  successCount++;
                } else if (response.status === 403) {
                  handleTokenExpired();
                  return;
                } else {
                  const errorData = await response.json();
                  failedDays.push({
                    date: singleDayData.vacation_date,
                    error: errorData.error || 'خطأ غير معروف'
                  });
                }
              } catch (error) {
                console.error(`خطأ في حفظ اليوم ${singleDayData.vacation_date}:`, error);
                failedDays.push({
                  date: singleDayData.vacation_date,
                  error: 'خطأ في الاتصال'
                });
              }

              workDaysAdded++;
            }
            currentDate.setDate(currentDate.getDate() + 1);
          }

          // عرض نتائج الحفظ
          if (successCount > 0 && failedDays.length === 0) {
            alert(`تم حفظ جميع أيام الإجازة بنجاح (${successCount} أيام)`);
          } else if (successCount > 0 && failedDays.length > 0) {
            let message = `تم حفظ ${successCount} أيام بنجاح.\n\nفشل في حفظ الأيام التالية:\n`;
            failedDays.forEach(day => {
              message += `- ${day.date}: ${day.error}\n`;
            });
            alert(message);
          } else if (failedDays.length > 0) {
            let message = `فشل في حفظ جميع أيام الإجازة:\n\n`;
            failedDays.forEach(day => {
              message += `- ${day.date}: ${day.error}\n`;
            });
            alert(message);
          } else {
            alert('فشل في حفظ الإجازة - خطأ غير معروف');
          }

          displayAddedVacations();
          resetVacationForm();
          await loadVacations();
        }
      }
    } catch (error) {
      console.error('Error saving vacation:', error);
      alert('حدث خطأ أثناء حفظ الإجازة');
    }
  }

  // إعادة تعيين نموذج الإجازة
  function resetForm() {
    resetVacationForm();
  }

  // إعادة تعيين نموذج الإجازة
  function resetVacationForm() {
    employeeSearchAdd.value = ''; // إعادة تعيين حقل البحث
    employeeCode.value = ''; // إعادة تعيين كود الموظف
    employeeName.value = ''; // إعادة تعيين اسم الموظف
    employeeDepartment.value = ''; // إعادة تعيين الإدارة
    vacationType.value = '';
    officialHoliday.value = '';
    officialHolidayGroup.style.display = 'none';

    // إعادة تعيين التاريخ
    if (vacationDate) vacationDate.value = '';
    if (daysCount) {
      daysCount.value = '1';
    }
    
    // مسح قائمة الاقتراحات
    const datalist = document.getElementById('employeeSearchSuggestions');
    if (datalist) {
      datalist.innerHTML = '';
    }
    
    // إعادة تعيين زر الحفظ
    const saveBtn = document.getElementById('saveVacation');
    saveBtn.textContent = 'حفظ الإجازة';
    saveBtn.removeAttribute('data-edit-id');
  }

  // دالة لإزالة الهمزات والتاء المربوطة والمفتوحة من النص
  function normalizeArabicText(text) {
    if (!text) return '';
    return text
      .replace(/[أإآا]/g, 'ا') // توحيد الألف بأشكالها المختلفة
      .replace(/[ةه]/g, 'ه')   // توحيد التاء المربوطة والهاء
      .replace(/ى/g, 'ي')      // توحيد الياء المقصورة
      .replace(/ؤ/g, 'و')      // توحيد الواو والهمزة على الواو
      .replace(/ئ/g, 'ي')      // توحيد الياء والهمزة على الياء
      .trim();
  }

  // عرض الإجازات في الجدول
  function displayVacations(showAll = false) {
    // التحقق من وجود فلاتر بحث
    const filterDepartment = departmentFilterView.value;
    const filterStartDate = startDateFilter && startDateFilter.value ? new Date(startDateFilter.value) : null;
    const filterEndDate = endDateFilter && endDateFilter.value ? new Date(endDateFilter.value) : null;
    const filterName = document.getElementById('nameSearch').value.trim();

    // إذا لم يتم إدخال أي معايير بحث ولم يتم طلب عرض الكل، قم بإخفاء الجدول وإظهار رسالة
    if (!showAll && !filterDepartment && !filterStartDate && !filterEndDate && !filterName) {
      vacationsTableBody.innerHTML = '<tr><td colspan="10" class="no-results">يرجى الضغط على زر "بحث" أو "عرض الكل" لعرض البيانات</td></tr>';
      return;
    }
    
    // تطبيق الفلاتر
    let filteredVacations = [...vacations];
    
    // تطبيق الفلاتر فقط إذا لم يتم طلب عرض الكل
    if (!showAll) {
      if (filterDepartment) {
        filteredVacations = filteredVacations.filter(v => v.department === filterDepartment);
      }
      
      if (filterStartDate && filterEndDate) {
        filteredVacations = filteredVacations.filter(v => {
          // استخدام vacation_date فقط
          const vacationDate = v.vacation_date;

          if (!vacationDate) return false;

          // تعيين الوقت لبداية اليوم للتاريخ المطلوب البحث عنه
          const searchStartDate = new Date(filterStartDate);
          searchStartDate.setHours(0, 0, 0, 0);

          // تعيين الوقت لنهاية اليوم للتاريخ النهائي
          const searchEndDate = new Date(filterEndDate);
          searchEndDate.setHours(23, 59, 59, 999);

          // تعيين الوقت لبداية اليوم لتاريخ الإجازة
          const vacDate = new Date(vacationDate);
          vacDate.setHours(0, 0, 0, 0);

          // التحقق من أن تاريخ الإجازة ضمن فترة البحث
          return (vacDate >= searchStartDate && vacDate <= searchEndDate);
        });
      }
      
      // تطبيق فلتر البحث بالاسم مع تجاهل الهمزة والتاء
      if (filterName) {
        const normalizedSearchName = normalizeArabicText(filterName);
        filteredVacations = filteredVacations.filter(v => {
          const employeeName = v.employee_name || '';
          const normalizedEmployeeName = normalizeArabicText(employeeName);
          return normalizedEmployeeName.includes(normalizedSearchName) ||
                 (v.employee_code && v.employee_code.toString().includes(filterName));
        });
      }
    }
    
    // تجميع الإجازات حسب الموظف
    const employeeVacations = {};
    
    filteredVacations.forEach(v => {
      if (!employeeVacations[v.employee_code]) {
        employeeVacations[v.employee_code] = {
          code: v.employee_code,
          name: v.employee_name,
          department: v.department,
          casual: 0,
          permission: 0,
          absence: 0,
          annual: 0,
          unpaid: 0,
          sick: 0,
          official: 0,
          vacationDetails: [] // إضافة مصفوفة لتخزين تفاصيل الإجازات
        };
      }
      
      // إضافة تفاصيل الإجازة إلى مصفوفة التفاصيل
      // الآن التاريخ يأتي من الخادم بصيغة YYYY-MM-DD مباشرة
      employeeVacations[v.employee_code].vacationDetails.push({
        id: v.id, // إضافة معرف الإجازة
        type: v.vacation_type,
        date: v.vacation_date, // التاريخ بصيغة صحيحة من الخادم
        days: v.days_count,
        officialType: v.official_type
      });
      
      // زيادة عدد الإجازات حسب النوع
      switch (v.vacation_type) {
        case 'casual':
          employeeVacations[v.employee_code].casual += parseInt(v.days_count) || 0;
          break;
        case 'permission':
          employeeVacations[v.employee_code].permission += parseInt(v.days_count) || 0;
          break;
        case 'absence':
          employeeVacations[v.employee_code].absence += parseInt(v.days_count) || 0;
          break;
        case 'annual':
          employeeVacations[v.employee_code].annual += parseInt(v.days_count) || 0;
          break;
        case 'unpaid':
          employeeVacations[v.employee_code].unpaid += parseInt(v.days_count) || 0;
          break;
        case 'sick':
          employeeVacations[v.employee_code].sick += parseInt(v.days_count) || 0;
          break;
        case 'official':
          employeeVacations[v.employee_code].official += parseInt(v.days_count) || 0;
          break;
      }
    });
    
    // عرض البيانات في الجدول مع دعم الصفحات
    vacationsTableBody.innerHTML = '';

    // إذا لم تكن هناك نتائج بعد تطبيق الفلاتر
    if (Object.keys(employeeVacations).length === 0) {
      vacationsTableBody.innerHTML = '<tr><td colspan="10" class="no-results">لا توجد نتائج تطابق معايير البحث</td></tr>';
      updatePaginationControlsVacations();
      return;
    }

    // ترتيب الموظفين حسب الاسم
    const sortedEmployees = Object.values(employeeVacations).sort((a, b) => {
      return (a.name || '').localeCompare(b.name || '', 'ar');
    });

    // تحديث العدد الإجمالي
    totalVacations = sortedEmployees.length;

    // حساب البيانات للصفحة الحالية
    const startIndex = (currentPageVacations - 1) * itemsPerPageVacations;
    const endIndex = startIndex + itemsPerPageVacations;
    const currentPageEmployees = sortedEmployees.slice(startIndex, endIndex);

    // عرض موظفي الصفحة الحالية فقط
    currentPageEmployees.forEach(empVac => {
      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${empVac.code || 'غير محدد'}</td>
        <td>${empVac.name || 'غير محدد'}</td>
        <td>${empVac.casual || 0}</td>
        <td>${empVac.permission || 0}</td>
        <td>${empVac.absence || 0}</td>
        <td>${empVac.annual || 0}</td>
        <td>${empVac.unpaid || 0}</td>
        <td>${empVac.sick || 0}</td>
        <td>${empVac.official || 0}</td>
        <td><button class="details-btn" data-code="${empVac.code || ''}">عرض التفاصيل</button></td>
      `;

      // التحقق من وجود إجازات من العام المالي السابق
      let hasEmployeePreviousYearVacations = false;
      if (empVac.vacationDetails && empVac.vacationDetails.length > 0) {
        hasEmployeePreviousYearVacations = empVac.vacationDetails.some(detail =>
          isBeforeJune26(detail.date)
        );
      }

      // إضافة اللون الأحمر للصفوف التي تحتوي على إجازات من العام المالي السابق
      if (hasEmployeePreviousYearVacations) {
        row.style.backgroundColor = '#ffebee'; // خلفية حمراء فاتحة
        row.style.color = '#c62828'; // نص أحمر داكن
        row.title = 'يحتوي على إجازات من العام المالي السابق';
      }

      vacationsTableBody.appendChild(row);

      // إضافة مستمع حدث لزر عرض التفاصيل
      const detailsBtn = row.querySelector('.details-btn');
      detailsBtn.addEventListener('click', () => showVacationDetails(empVac));
    });

    // تحديث عناصر التحكم في الصفحات
    updatePaginationControlsVacations();
  }

  // تصدير بيانات الإجازات إلى Excel
  function exportVacationsToExcel() {
    // إنشاء مصفوفة البيانات
    const data = [];
    
    // إضافة رأس الجدول
    data.push(['الكود', 'الاسم', 'عارضة', 'غياب بإذن', 'غياب بدون إذن', 'سنوية', 'بدون راتب', 'مرضي', 'إجازات خارج الرصيد']);
    
    // إضافة صفوف البيانات
    const rows = vacationsTableBody.querySelectorAll('tr');
    rows.forEach(row => {
      const rowData = [];
      // نتجاهل الخلية الأخيرة التي تحتوي على زر عرض التفاصيل
      const cells = row.querySelectorAll('td');
      for (let i = 0; i < cells.length - 1; i++) {
        rowData.push(cells[i].textContent);
      }
      data.push(rowData);
    });
    
    // تحويل البيانات إلى CSV
    let csvContent = "\uFEFF"; // إضافة BOM للدعم الصحيح للغة العربية
    data.forEach(rowArray => {
      const row = rowArray.join(";\t"); // استخدام الفاصلة المنقوطة وعلامة التبويب لتجنب مشاكل الفواصل
      csvContent += row + "\r\n";
    });
    
    // إنشاء رابط التنزيل
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", "تقرير_الإجازات.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
  
  // تصدير تفاصيل إجازات موظف معين إلى Excel
  function exportVacationDetailsToExcel() {
    // الحصول على معلومات الموظف
    const employeeCode = document.getElementById('detailsEmployeeCode').textContent;
    const employeeName = document.getElementById('detailsEmployeeFullName').textContent;
    const employeeDepartment = document.getElementById('detailsEmployeeDepartment').textContent;
    const leaveBalance = document.getElementById('detailsLeaveBalance').textContent;
    const leaveUsed = document.getElementById('detailsLeaveUsed').textContent;
    const leaveRemaining = document.getElementById('detailsLeaveRemaining').textContent;
    
    // إنشاء مصفوفة البيانات
    const data = [];
    
    // إضافة معلومات الموظف
    data.push(['معلومات الموظف']);
    data.push(['الكود', employeeCode]);
    data.push(['الاسم', employeeName]);
    data.push(['الإدارة', employeeDepartment]);
    data.push(['رصيد الإجازات', leaveBalance]);
    data.push(['الإجازات المستخدمة', leaveUsed]);
    data.push(['الإجازات المتبقية', leaveRemaining]);
    data.push([]); // سطر فارغ
    
    // إضافة رأس جدول الإجازات
    data.push(['نوع الإجازة', 'التاريخ', 'عدد الأيام', 'نوع الإجازة']);
    
    // إضافة صفوف بيانات الإجازات
    const rows = document.getElementById('vacationDetailsTableBody').querySelectorAll('tr');
    rows.forEach(row => {
      const rowData = [];
      // استخدام الأعمدة الأربعة الأولى فقط (بدون عمود الإجراءات)
      const cells = row.querySelectorAll('td');
      for (let i = 0; i < 4; i++) {
        rowData.push(cells[i].textContent);
      }
      data.push(rowData);
    });
    
    // تحويل البيانات إلى CSV
    let csvContent = "\uFEFF"; // إضافة BOM للدعم الصحيح للغة العربية
    data.forEach(rowArray => {
      const row = rowArray.join(";\t"); // استخدام الفاصلة المنقوطة وعلامة التبويب لتجنب مشاكل الفواصل
      csvContent += row + "\r\n";
    });
    
    // إنشاء رابط التنزيل
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", `تفاصيل_إجازات_${employeeName}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
  
  // طباعة تفاصيل الإجازات
  // طباعة تفاصيل الإجازات
  function printVacationDetails() {
    const employeeCode = document.getElementById('detailsEmployeeCode').textContent;
    const employeeName = document.getElementById('detailsEmployeeFullName').textContent;
    const employeeDepartment = document.getElementById('detailsEmployeeDepartment').textContent;
    const leaveBalance = document.getElementById('detailsLeaveBalance').textContent;
    const leaveUsed = document.getElementById('detailsLeaveUsed').textContent;
    const leaveRemaining = document.getElementById('detailsLeaveRemaining').textContent;
    
    // إنشاء نافذة طباعة جديدة
    const printWindow = window.open('', '_blank');
    
    // إنشاء محتوى HTML للطباعة
    let printContent = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>تفاصيل إجازات ${employeeName}</title>
        <style>
          body {
            font-family: 'Arial', sans-serif;
            margin: 20px;
            direction: rtl;
          }
          h1 {
            text-align: center;
            margin-bottom: 20px;
          }
          .employee-info {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
          }
          th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
          }
          th {
            background-color: #2196F3;
            color: white;
            font-weight: bold;
          }
          tr:nth-child(even) {
            background-color: #f9f9f9;
          }
          @media print {
            body {
              -webkit-print-color-adjust: exact;
              print-color-adjust: exact;
            }
          }
        </style>
      </head>
      <body>
        <h1>تفاصيل إجازات الموظف</h1>
        <div class="employee-info">
          <p><strong>الكود:</strong> ${employeeCode}</p>
          <p><strong>الاسم:</strong> ${employeeName}</p>
          <p><strong>الإدارة:</strong> ${employeeDepartment}</p>
          <p><strong>رصيد الإجازات:</strong> ${leaveBalance}</p>
          <p><strong>الإجازات المستخدمة:</strong> ${leaveUsed}</p>
          <p><strong>الإجازات المتبقية:</strong> ${leaveRemaining}</p>
        </div>
        <table>
          <thead>
            <tr>
              <th>نوع الإجازة</th>
              <th>التاريخ</th>
              <th>عدد الأيام</th>
              <th>نوع الإجازة</th>
            </tr>
          </thead>
          <tbody>
    `;
    
    // إضافة صفوف بيانات الإجازات
    const rows = document.getElementById('vacationDetailsTableBody').querySelectorAll('tr');
    rows.forEach(row => {
      printContent += '<tr>';
      // استخدام الأعمدة الأربعة الأولى فقط (بدون عمود الإجراءات)
      const cells = row.querySelectorAll('td');
      for (let i = 0; i < 4; i++) {
        printContent += `<td>${cells[i].textContent}</td>`;
      }
      printContent += '</tr>';
    });
    
    // إكمال محتوى HTML
    printContent += `
          </tbody>
        </table>
      </body>
      </html>
    `;
    
    // كتابة المحتوى في نافذة الطباعة وطباعته
    printWindow.document.open();
    printWindow.document.write(printContent);
    printWindow.document.close();
    
    // الانتظار حتى يتم تحميل المحتوى ثم الطباعة
    printWindow.onload = function() {
      printWindow.print();
    };
  }

  // تبديل التبويبات
  function switchTab(tabId) {
    console.log(`تبديل إلى التبويب: ${tabId}`);

    // التحقق من الصلاحيات قبل التبديل
    if (tabId === 'vacation-reports') {
      const permissions = JSON.parse(localStorage.getItem('permissions') || '{}');
      if (!permissions.view_vacation_reports) {
        alert('ليس لديك صلاحية لعرض تقارير الإجازات');
        return;
      }
    }

    // تحديث الأزرار النشطة
    tabBtns.forEach(btn => {
      if (btn.dataset.tab === tabId) {
        btn.classList.add('active');
      } else {
        btn.classList.remove('active');
      }
    });

    // تحديث محتوى التبويبات
    tabContents.forEach(content => {
      if (content.id === tabId) {
        content.style.display = 'block';
        // إذا كان التبويب هو عرض الإجازات، لا نعرض البيانات تلقائياً
        if (tabId === 'view-vacations') {
          console.log('تم التبديل إلى تبويب عرض الإجازات - البيانات مخفية افتراضياً');
          // التأكد من إخفاء البيانات وإظهار الرسالة التوجيهية
          if (vacationsTableBody) {
            vacationsTableBody.innerHTML = '<tr><td colspan="10" class="no-results">يرجى الضغط على زر "بحث" أو "عرض الكل" لعرض البيانات</td></tr>';
          }
        }

      } else {
        content.style.display = 'none';
      }
    });
  }

  // التحقق من التبويب المحدد من البطاقات عند تحميل الصفحة
  function checkSelectedTab() {
    const selectedTab = localStorage.getItem('selectedVacationTab');
    if (selectedTab) {
      // إزالة التبويب المحفوظ لتجنب التكرار
      localStorage.removeItem('selectedVacationTab');

      // التبديل إلى التبويب المحدد
      switchTab(selectedTab);
    } else {
      // التبديل إلى التبويب الافتراضي (إضافة إجازة)
      switchTab('add-vacation');
    }
  }

  // عرض تفاصيل الإجازات في النافذة المنبثقة
  // عرض تفاصيل الإجازات في النافذة المنبثقة
  async function showVacationDetails(employeeVacation) {
    // حفظ بيانات الموظف الحالي لإعادة التحميل عند تغيير الفلتر
    currentEmployeeForDetails = employeeVacation;
    // الحصول على عناصر DOM للنافذة المنبثقة
    const vacationDetailsModal = document.getElementById('vacationDetailsModal');
    const detailsEmployeeCode = document.getElementById('detailsEmployeeCode');
    const detailsEmployeeFullName = document.getElementById('detailsEmployeeFullName');
    const detailsEmployeeDepartment = document.getElementById('detailsEmployeeDepartment');
    const vacationDetailsTableBody = document.getElementById('vacationDetailsTableBody');

    // الحصول على عناصر رصيد الإجازات
    const detailsLeaveBalance = document.getElementById('detailsLeaveBalance');
    const detailsLeaveUsed = document.getElementById('detailsLeaveUsed');
    const detailsLeaveRemaining = document.getElementById('detailsLeaveRemaining');

    // تعبئة معلومات الموظف
    detailsEmployeeCode.textContent = employeeVacation.code || 'غير محدد';
    detailsEmployeeFullName.textContent = employeeVacation.name || 'غير محدد';
    detailsEmployeeDepartment.textContent = employeeVacation.department || 'غير محدد';

    // عرض رسالة تحميل أثناء جلب البيانات
    detailsLeaveBalance.textContent = 'جاري التحميل...';
    detailsLeaveUsed.textContent = 'جاري التحميل...';
    detailsLeaveRemaining.textContent = 'جاري التحميل...';

    // جلب رصيد الإجازات الصحيح من الخادم باستخدام الدالة المحدثة
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/api/employees/${employeeVacation.code}/leave-balance`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const leaveData = await response.json();
        console.log('بيانات رصيد الإجازات من الخادم:', leaveData);
        console.log('كود الموظف المرسل:', employeeVacation.code);

        // التحقق من وجود البيانات
        if (leaveData && typeof leaveData === 'object') {
          detailsLeaveBalance.textContent = leaveData.calculated_balance || '30';
          detailsLeaveUsed.textContent = leaveData.used_days || '0';
          detailsLeaveRemaining.textContent = leaveData.remaining_days || leaveData.calculated_balance || '30';

          console.log('تم تعيين القيم:', {
            balance: leaveData.calculated_balance,
            used: leaveData.used_days,
            remaining: leaveData.remaining_days
          });
        } else {
          console.error('البيانات المستلمة غير صحيحة:', leaveData);
          throw new Error('البيانات المستلمة غير صحيحة');
        }
      } else {
        console.error('فشل في جلب رصيد الإجازات من الخادم، الاستجابة:', response.status);
        const responseText = await response.text();
        console.error('نص الاستجابة:', responseText);

        // حساب محلي كبديل باستخدام الفترة الصحيحة
        const localBalance = await calculateLocalLeaveBalance(employeeVacation.code);
        detailsLeaveBalance.textContent = localBalance.balance;
        detailsLeaveUsed.textContent = localBalance.used;
        detailsLeaveRemaining.textContent = localBalance.remaining;
      }
    } catch (error) {
      console.error('خطأ في جلب رصيد الإجازات:', error);
      // حساب محلي كبديل
      const localBalance = await calculateLocalLeaveBalance(employeeVacation.code);
      detailsLeaveBalance.textContent = localBalance.balance;
      detailsLeaveUsed.textContent = localBalance.used;
      detailsLeaveRemaining.textContent = localBalance.remaining;
    }
    
    // تعبئة جدول تفاصيل الإجازات
    vacationDetailsTableBody.innerHTML = '<tr><td colspan="5">جاري تحميل الإجازات...</td></tr>';

    // ملاحظة: فلاتر التاريخ في نافذة التفاصيل ستطبق لاحقاً على البيانات المحملة

    // استخدام الإجازات المفلترة من الجدول الرئيسي إذا كانت متوفرة
    let allEmployeeVacations = [];

    // التحقق من وجود تفاصيل الإجازات المفلترة في بيانات الموظف
    if (employeeVacation.vacationDetails && employeeVacation.vacationDetails.length > 0) {
      console.log('استخدام الإجازات المفلترة من الجدول الرئيسي:', employeeVacation.vacationDetails);
      console.log('عدد الإجازات المفلترة:', employeeVacation.vacationDetails.length);
      // تحويل تفاصيل الإجازات إلى التنسيق المطلوب
      allEmployeeVacations = employeeVacation.vacationDetails.map(detail => ({
        id: detail.id,
        vacation_type: detail.type,
        vacation_date: detail.date,
        days_count: detail.days,
        official_type: detail.officialType
      }));
    } else {
      // إذا لم تكن هناك تفاصيل مفلترة، جلب جميع إجازات الموظف من الخادم
      console.log('لا توجد إجازات مفلترة، جلب جميع إجازات الموظف من الخادم');
      try {
        const token = localStorage.getItem('token');
        const vacationsResponse = await fetch(`${API_URL}/api/vacations/employee/${employeeVacation.code}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (vacationsResponse.ok) {
          allEmployeeVacations = await vacationsResponse.json();
          console.log('إجازات الموظف من الخادم:', allEmployeeVacations);
        } else {
          console.error('فشل في جلب إجازات الموظف من الخادم:', vacationsResponse.status);
          // استخدام البيانات المحلية كبديل
          allEmployeeVacations = getEmployeeVacationsFromLocal(employeeVacation.code);
        }
      } catch (error) {
        console.error('خطأ في جلب إجازات الموظف:', error);
        // استخدام البيانات المحلية كبديل
        allEmployeeVacations = getEmployeeVacationsFromLocal(employeeVacation.code);
      }
    }

    // مسح رسالة التحميل
    vacationDetailsTableBody.innerHTML = '';

    console.log('إجازات الموظف النهائية:', allEmployeeVacations);
    console.log('عدد الإجازات:', allEmployeeVacations ? allEmployeeVacations.length : 'غير محدد');

    // التحقق من وجود إجازات
    if (!allEmployeeVacations || allEmployeeVacations.length === 0) {
      console.log('لا توجد إجازات للموظف، عرض رسالة فارغة');
      vacationDetailsTableBody.innerHTML = '<tr><td colspan="4" class="no-results">لا توجد إجازات مسجلة لهذا الموظف</td></tr>';
    } else {
      console.log('تم العثور على إجازات، عرضها في الجدول');

      // عرض جميع إجازات الموظف بدون فلترة إضافية في نافذة التفاصيل
      console.log('عرض جميع إجازات الموظف في نافذة التفاصيل:', allEmployeeVacations.length);

      // فحص وجود إجازات من العام المالي السابق لإظهار الملحوظة
      let hasDetailsPagePreviousYearVacations = false;

      // ترتيب الإجازات حسب التاريخ (الأحدث أولاً)
      const sortedVacations = allEmployeeVacations.sort((a, b) => {
        const dateA = new Date(a.vacation_date || a.date);
        const dateB = new Date(b.vacation_date || b.date);
        return dateB - dateA;
      });

      console.log('الإجازات النهائية بعد الترتيب:', sortedVacations.length);

      // التحقق من وجود إجازات بعد الفلترة
      if (sortedVacations.length === 0) {
        vacationDetailsTableBody.innerHTML = '<tr><td colspan="4" class="no-results">لا توجد إجازات في الفترة المحددة</td></tr>';
        return;
      }

      sortedVacations.forEach(vacation => {
      const row = document.createElement('tr');

      // تحديد نوع الإجازة (يمكن أن يكون vacation.type أو vacation.vacation_type)
      const vacationType = vacation.vacation_type || vacation.type;

      // تحويل نوع الإجازة إلى النص العربي
      let vacationTypeText = '';
      switch (vacationType) {
        case 'casual':
          vacationTypeText = 'إجازة عارضة';
          break;
        case 'permission':
          vacationTypeText = 'غياب بإذن';
          break;
        case 'absence':
          vacationTypeText = 'غياب بدون إذن';
          break;
        case 'annual':
          vacationTypeText = 'إجازة سنوية';
          break;
        case 'unpaid':
          vacationTypeText = 'إجازة بدون راتب';
          break;
        case 'sick':
          vacationTypeText = 'إجازة مرضية';
          break;
        case 'official':
          vacationTypeText = 'إجازات خارج الرصيد';
          break;
        default:
          vacationTypeText = vacationType || 'غير محدد';
      }

      // تحويل تاريخ الإجازة إلى تنسيق أكثر قراءة مع تجنب مشكلة نقص اليوم
      const vacationDate = vacation.vacation_date || vacation.date;
      const formattedDate = formatDateSafe(vacationDate);
      
      // تحديد نوع الإجازة
      const officialType = vacation.official_type || vacation.officialType;

      // تحويل نوع الإجازة إلى النص العربي
      let officialTypeText = '';
      if (vacationType === 'official' && officialType) {
        const officialTypeMap = {
          // الإجازات خارج الرصيد الموجودة
          'police_day': 'عيد الشرطة (25 يناير)',
          'sinai_day': 'عيد تحرير سيناء',
          'labor_day': 'عيد العمال',
          'june_revolution': 'عيد ثورة 30 يونيو',
          'july_revolution': 'عيد ثورة 23 يوليو',
          'armed_forces_day': 'عيد القوات المسلحة (6 أكتوبر)',
          'christmas': 'عيد الميلاد المجيد',
          'sham_el_nessim': 'عيد شم النسيم',
          'islamic_new_year': 'رأس السنة الهجرية',
          'prophet_birthday': 'المولد النبوي الشريف',
          'eid_adha': 'عيد الأضحى',
          'eid_fitr': 'عيد الفطر',
          // الأنواع الجديدة المضافة بعد عيد الفطر
          'emergency': 'اجازة اضطرارية',
          'birth': 'مولود',
          'maternity': 'اجازة وضع',
          'marriage': 'زواج',
          'death_first_degree': 'الوفاة من الدرجة الاولى',
          'military_service': 'الاستدعاء للجيش',
          'exams': 'الامتحانات',
          'pilgrimage': 'الحج والعمرة',
          // الأنواع القديمة للتوافق مع البيانات الموجودة
          'new_year': 'رأس السنة الميلادية',
          'coptic_christmas': 'عيد الميلاد المجيد',
          'january_revolution': 'ثورة 25 يناير',
          'sinai_liberation': 'تحرير سيناء',
          'arafat_day': 'يوم عرفة',
          'eid_al_adha': 'عيد الأضحى المبارك',
          'eid_al_adha_2': 'عيد الأضحى المبارك (اليوم الثاني)',
          'eid_al_adha_3': 'عيد الأضحى المبارك (اليوم الثالث)',
          'eid_al_fitr': 'عيد الفطر المبارك',
          'eid_al_fitr_2': 'عيد الفطر المبارك (اليوم الثاني)',
          'eid_al_fitr_3': 'عيد الفطر المبارك (اليوم الثالث)',
          'other': 'أخرى'
        };

        officialTypeText = officialTypeMap[officialType] || officialType;
      }

      // تحديد عدد الأيام
      const daysCount = vacation.days_count || vacation.days || 1;

      row.innerHTML = `
        <td>${vacationTypeText}</td>
        <td>${formattedDate}</td>
        <td>${daysCount}</td>
        <td>${officialTypeText}</td>
      `;

      // إضافة اللون الأحمر للإجازات قبل 26-6 (العام المالي السابق)
      if (isBeforeJune26(vacationDate)) {
        row.style.backgroundColor = '#ffebee'; // خلفية حمراء فاتحة
        row.style.color = '#c62828'; // نص أحمر داكن
        row.style.fontWeight = 'bold';
        row.title = 'إجازة من العام المالي السابق (قبل 26-6)';
        hasDetailsPagePreviousYearVacations = true; // تحديث المتغير
      }

      vacationDetailsTableBody.appendChild(row);
    });

    // إظهار/إخفاء ملحوظة الإجازات من العام السابق في نافذة التفاصيل
    const detailsPreviousYearNote = document.getElementById('detailsPreviousYearNote');
    if (detailsPreviousYearNote) {
      if (hasDetailsPagePreviousYearVacations) {
        detailsPreviousYearNote.style.display = 'inline-block';
      } else {
        detailsPreviousYearNote.style.display = 'none';
      }
    }
    }

    // تم حذف أزرار الإجراءات من عرض التفاصيل
    
    // عرض النافذة المنبثقة
    vacationDetailsModal.style.display = 'block';
  }

  // متغير لتخزين بيانات الموظف الحالي في تفاصيل الإجازات
  let currentEmployeeForDetails = null;

  // دالة حساب تاريخ نهاية الإجازة
  function calculateVacationEndDate() {
    const startDate = vacationDate.value;
    const days = parseInt(daysCount.value) || 1;
    const endDateInfo = document.getElementById('vacationEndDateInfo');
    const calculatedEndDate = document.getElementById('calculatedEndDate');

    if (startDate && days > 0) {
      const startDateObj = new Date(startDate);
      let currentDate = new Date(startDateObj);
      let workDaysAdded = 0;

      // إضافة أيام العمل (تجاهل الجمعة فقط)
      while (workDaysAdded < days) {
        const dayOfWeek = currentDate.getDay();

        // إذا لم يكن الجمعة (5) - الجمعة هو العطلة الوحيدة
        if (dayOfWeek !== 5) {
          workDaysAdded++;
        }

        // إذا لم نصل للعدد المطلوب، انتقل لليوم التالي
        if (workDaysAdded < days) {
          currentDate.setDate(currentDate.getDate() + 1);
        }
      }

      // تنسيق التاريخ
      const day = String(currentDate.getDate()).padStart(2, '0');
      const month = String(currentDate.getMonth() + 1).padStart(2, '0');
      const year = currentDate.getFullYear();
      const formattedEndDate = `${day}/${month}/${year}`;

      calculatedEndDate.textContent = formattedEndDate;
      endDateInfo.style.display = 'block';
    } else {
      endDateInfo.style.display = 'none';
    }
  }

  // إضافة مستمعي الأحداث
  function setupEventListeners() {
    // غلاق النافذة المنبثقة لتفاصيل الإجازات
    document.querySelector('.close-details-btn').addEventListener('click', () => {
      vacationDetailsModal.style.display = 'none';
      currentEmployeeForDetails = null;
    });

    // لا توجد حاجة لمستمعي أحداث فلتر التاريخ في نافذة التفاصيل
    // نافذة التفاصيل تعرض جميع إجازات الموظف
    
    // أحداث التبويبات
    tabBtns.forEach(btn => {
      btn.addEventListener('click', () => switchTab(btn.dataset.tab));
    });

    // لا حاجة لحساب تاريخ النهاية بعد الآن - نستخدم تاريخ الإجازة فقط
    
    // أحداث نموذج الإجازة
    // إضافة مستمعي الأحداث لحقل البحث الجديد
    if (employeeSearchAdd) {
      employeeSearchAdd.addEventListener('input', function() {
        handleEmployeeSearch(this.value, 'employeeSearchSuggestions');
      });
      employeeSearchAdd.addEventListener('change', function() {
        handleEmployeeSelection(this.value);
      });
    }
    vacationType.addEventListener('change', updateVacationTypeFields);
    saveVacationBtn.addEventListener('click', saveVacation);
    resetFormBtn.addEventListener('click', resetForm);

    // إضافة مستمعي الأحداث لحساب تاريخ نهاية الإجازة
    if (vacationDate) {
      vacationDate.addEventListener('change', calculateVacationEndDate);
    }
    if (daysCount) {
      daysCount.addEventListener('input', calculateVacationEndDate);
    }

    // أحداث عرض الإجازات
    searchVacationsBtn.addEventListener('click', () => {
      currentPageVacations = 1; // إعادة تعيين الصفحة عند البحث
      displayVacations(false);
    });
    const showAllVacationsBtn = document.getElementById('showAllVacationsBtn');
    showAllVacationsBtn.addEventListener('click', () => {
      currentPageVacations = 1; // إعادة تعيين الصفحة عند عرض الكل
      displayVacations(true);
    });
    resetVacationsBtn.addEventListener('click', () => {
      departmentFilterView.value = '';
      if (startDateFilter) startDateFilter.value = '';
      if (endDateFilter) endDateFilter.value = '';
      document.getElementById('nameSearch').value = '';
      // إخفاء البيانات بعد إعادة التعيين
      vacationsTableBody.innerHTML = '<tr><td colspan="10" class="no-results">يرجى الضغط على زر "بحث" أو "عرض الكل" لعرض البيانات</td></tr>';
    });
    exportVacationsBtn.addEventListener('click', exportVacationsToExcel);
    
    // إضافة مستمع حدث لزر مسح جميع الإجازات
    const clearAllVacationsBtn = document.getElementById('clearAllVacationsBtn');
    if (clearAllVacationsBtn) {
      clearAllVacationsBtn.addEventListener('click', clearAllVacations);
    }
    document.getElementById('printVacationDetails').addEventListener('click', printVacationDetails);
    document.getElementById('exportVacationDetailsToExcel').addEventListener('click', exportVacationDetailsToExcel);
  }

  // عرض الإجازات المضافة في الجدول مع دعم الصفحات
  function displayAddedVacations() {
    if (!addedVacationsTableBody) return;

    addedVacationsTableBody.innerHTML = '';

    if (addedVacations.length === 0) {
      const emptyRow = document.createElement('tr');
      emptyRow.innerHTML = '<td colspan="8" class="no-results">لم يتم إضافة أي إجازات بعد</td>';
      addedVacationsTableBody.appendChild(emptyRow);
      updatePaginationControlsAddedVacations();
      return;
    }

    // ترتيب الإجازات حسب تاريخ الإضافة (الأحدث أولاً)
    const sortedVacations = [...addedVacations]
      .sort((a, b) => {
        // ترتيب حسب timestamp (الأحدث أولاً)
        return (b.added_timestamp || 0) - (a.added_timestamp || 0);
      });

    // تحديث العدد الإجمالي
    totalAddedVacations = sortedVacations.length;

    // حساب البيانات للصفحة الحالية
    const startIndex = (currentPageAddedVacations - 1) * itemsPerPageAddedVacations;
    const endIndex = startIndex + itemsPerPageAddedVacations;
    const currentPageVacations = sortedVacations.slice(startIndex, endIndex);

    // عرض إجازات الصفحة الحالية فقط
    currentPageVacations.forEach((vacation, index) => {
      const row = document.createElement('tr');

      // تمييز الصف الأول في الصفحة الأولى (آخر إضافة) بلون أخضر فاتح
      if (index === 0 && currentPageAddedVacations === 1) {
        row.style.backgroundColor = '#e8f5e8';
      }

      // تحويل نوع الإجازة إلى النص العربي
      let vacationTypeText = getVacationTypeText(vacation.vacation_type);

      // إضافة نوع الإجازة إذا كانت الإجازة خارج الرصيد
      if (vacation.vacation_type === 'official' && vacation.official_type) {
        const officialTypeMap = {
          // الإجازات خارج الرصيد الموجودة
          'police_day': 'عيد الشرطة (25 يناير)',
          'sinai_day': 'عيد تحرير سيناء',
          'labor_day': 'عيد العمال',
          'june_revolution': 'عيد ثورة 30 يونيو',
          'july_revolution': 'عيد ثورة 23 يوليو',
          'armed_forces_day': 'عيد القوات المسلحة (6 أكتوبر)',
          'christmas': 'عيد الميلاد المجيد',
          'sham_el_nessim': 'عيد شم النسيم',
          'islamic_new_year': 'رأس السنة الهجرية',
          'prophet_birthday': 'المولد النبوي الشريف',
          'eid_adha': 'عيد الأضحى',
          'eid_fitr': 'عيد الفطر',
          // الأنواع الجديدة المضافة بعد عيد الفطر
          'emergency': 'اجازة اضطرارية',
          'birth': 'مولود',
          'maternity': 'اجازة وضع',
          'marriage': 'زواج',
          'death_first_degree': 'الوفاة من الدرجة الاولى',
          'military_service': 'الاستدعاء للجيش',
          'exams': 'الامتحانات',
          'pilgrimage': 'الحج والعمرة',
          // الأنواع القديمة للتوافق مع البيانات الموجودة
          'new_year': 'رأس السنة الميلادية',
          'coptic_christmas': 'عيد الميلاد المجيد',
          'january_revolution': 'ثورة 25 يناير',
          'sinai_liberation': 'تحرير سيناء',
          'arafat_day': 'يوم عرفة',
          'eid_al_adha': 'عيد الأضحى المبارك',
          'eid_al_adha_2': 'عيد الأضحى المبارك (اليوم الثاني)',
          'eid_al_adha_3': 'عيد الأضحى المبارك (اليوم الثالث)',
          'eid_al_fitr': 'عيد الفطر المبارك',
          'eid_al_fitr_2': 'عيد الفطر المبارك (اليوم الثاني)',
          'eid_al_fitr_3': 'عيد الفطر المبارك (اليوم الثالث)',
          'other': 'أخرى'
        };

        const officialTypeText = officialTypeMap[vacation.official_type] || vacation.official_type;
        vacationTypeText += ` (${officialTypeText})`;
      }

      // عرض تاريخ الإجازة
      const vacationDateText = formatDateSafe(vacation.vacation_date);

      row.innerHTML = `
        <td><strong>${vacation.id || 'جديد'}</strong></td>
        <td>${vacation.employee_code}</td>
        <td>${vacation.employee_name}</td>
        <td>${vacation.department}</td>
        <td>${vacationTypeText}</td>
        <td>${vacationDateText}</td>
        <td>
          <button class="edit-added-btn" data-vacation-id="${vacation.id}" data-permission="can_edit">تعديل</button>
          <button class="delete-added-btn" data-vacation-id="${vacation.id}" data-permission="can_delete">حذف</button>
        </td>
      `;
      
      addedVacationsTableBody.appendChild(row);
    });

    // إضافة مستمعات الأحداث لأزرار الحذف والتعديل
    setupAddedVacationsButtons();

    // تحديث عناصر التحكم في الصفحات
    updatePaginationControlsAddedVacations();
  }
  
  // إعداد أزرار الحذف والتعديل للإجازات المضافة
  function setupAddedVacationsButtons() {
    // أزرار الحذف
    const deleteButtons = addedVacationsTableBody.querySelectorAll('.delete-added-btn');
    deleteButtons.forEach(button => {
      // تطبيق الصلاحيات
      if (hasPermission && !hasPermission('delete_vacation')) {
        button.style.display = 'none';
        return;
      }
      
      button.addEventListener('click', () => {
        const vacationId = button.getAttribute('data-vacation-id');

        if (confirm('هل أنت متأكد من حذف هذا اليوم من الإجازة؟')) {
          // البحث عن الإجازة في المصفوفة
          const vacationIndex = addedVacations.findIndex(v => v.id == vacationId);

          if (vacationIndex !== -1) {
            const deletedVacation = addedVacations.splice(vacationIndex, 1)[0];

            // حذف الإجازة من قاعدة البيانات
            if (deletedVacation.id) {
              deleteVacation(deletedVacation.id);
            }

            // تحديث العرض
            displayAddedVacations();
          } else {
            alert('خطأ: لم يتم العثور على الإجازة المحددة');
          }
        }
      });
    });
    
    // أزرار التعديل
    const editButtons = addedVacationsTableBody.querySelectorAll('.edit-added-btn');
    editButtons.forEach(button => {
      // تطبيق الصلاحيات
      if (hasPermission && !hasPermission('edit_vacation')) {
        button.style.display = 'none';
        return;
      }
      
      button.addEventListener('click', () => {
        const vacationId = button.getAttribute('data-vacation-id');
        const vacation = addedVacations.find(v => v.id == vacationId);

        if (vacation) {
          // فتح النافذة المنبثقة للتعديل
          const vacationIndex = addedVacations.findIndex(v => v.id == vacationId);
          openEditVacationModal(vacation, vacationIndex);
        } else {
          alert('خطأ: لم يتم العثور على الإجازة المحددة');
        }
      });
    });
  }

  // دوال التحكم في الصفحات للإجازات المضافة
  function updatePaginationControlsAddedVacations() {
    const totalPages = Math.ceil(totalAddedVacations / itemsPerPageAddedVacations);
    const paginationContainer = document.getElementById('paginationContainerAddedVacations');

    if (!paginationContainer) return;

    // إظهار التحكم في الصفحات دائماً (حتى لو كانت صفحة واحدة)
    // إخفاء فقط إذا لم توجد بيانات على الإطلاق
    if (totalAddedVacations === 0) {
      paginationContainer.style.display = 'none';
      return;
    }

    paginationContainer.style.display = 'flex';

    // تحديث معلومات الصفحة
    const pageInfo = document.getElementById('pageInfoAddedVacations');
    if (pageInfo) {
      const startItem = (currentPageAddedVacations - 1) * itemsPerPageAddedVacations + 1;
      const endItem = Math.min(currentPageAddedVacations * itemsPerPageAddedVacations, totalAddedVacations);
      pageInfo.textContent = `عرض ${startItem} - ${endItem} من أصل ${totalAddedVacations} إجازة`;
    }

    // تحديث أزرار التنقل
    const prevBtn = document.getElementById('prevPageAddedVacations');
    const nextBtn = document.getElementById('nextPageAddedVacations');

    if (prevBtn) prevBtn.disabled = currentPageAddedVacations <= 1;
    if (nextBtn) nextBtn.disabled = currentPageAddedVacations >= totalPages;

    // تحديث أرقام الصفحات
    updatePageNumbersAddedVacations(totalPages);
  }

  function updatePageNumbersAddedVacations(totalPages) {
    const pageNumbersContainer = document.getElementById('pageNumbersAddedVacations');
    if (!pageNumbersContainer) return;

    pageNumbersContainer.innerHTML = '';

    // إظهار رقم الصفحة حتى لو كانت صفحة واحدة فقط
    if (totalPages === 0) return;

    // حساب نطاق الصفحات المعروضة
    let startPage = Math.max(1, currentPageAddedVacations - 2);
    let endPage = Math.min(totalPages, currentPageAddedVacations + 2);

    // التأكد من عرض 5 صفحات على الأقل إذا كان ذلك ممكناً
    if (endPage - startPage < 4) {
      if (startPage === 1) {
        endPage = Math.min(totalPages, startPage + 4);
      } else if (endPage === totalPages) {
        startPage = Math.max(1, endPage - 4);
      }
    }

    // إضافة الصفحة الأولى إذا لم تكن في النطاق
    if (startPage > 1) {
      addPageButtonAddedVacations(1);
      if (startPage > 2) {
        const dots = document.createElement('span');
        dots.textContent = '...';
        dots.className = 'pagination-dots';
        pageNumbersContainer.appendChild(dots);
      }
    }

    // إضافة أرقام الصفحات في النطاق
    for (let i = startPage; i <= endPage; i++) {
      addPageButtonAddedVacations(i);
    }

    // إضافة الصفحة الأخيرة إذا لم تكن في النطاق
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        const dots = document.createElement('span');
        dots.textContent = '...';
        dots.className = 'pagination-dots';
        pageNumbersContainer.appendChild(dots);
      }
      addPageButtonAddedVacations(totalPages);
    }
  }

  function addPageButtonAddedVacations(pageNumber) {
    const pageNumbersContainer = document.getElementById('pageNumbersAddedVacations');
    const button = document.createElement('button');
    button.textContent = pageNumber;
    button.className = 'pagination-btn page-btn';
    if (pageNumber === currentPageAddedVacations) {
      button.classList.add('active');
    }
    button.addEventListener('click', () => goToPageAddedVacations(pageNumber));
    pageNumbersContainer.appendChild(button);
  }

  function goToPageAddedVacations(page) {
    const totalPages = Math.ceil(totalAddedVacations / itemsPerPageAddedVacations);
    if (page >= 1 && page <= totalPages) {
      currentPageAddedVacations = page;
      displayAddedVacations();
    }
  }

  function changePageAddedVacations(direction) {
    const newPage = currentPageAddedVacations + direction;
    goToPageAddedVacations(newPage);
  }

  // دوال التحكم في الصفحات لعرض الإجازات
  function updatePaginationControlsVacations() {
    const totalPages = Math.ceil(totalVacations / itemsPerPageVacations);
    const paginationContainer = document.getElementById('paginationContainerVacations');

    if (!paginationContainer) return;

    // إظهار التحكم في الصفحات دائماً (حتى لو كانت صفحة واحدة)
    // إخفاء فقط إذا لم توجد بيانات على الإطلاق
    if (totalVacations === 0) {
      paginationContainer.style.display = 'none';
      return;
    }

    paginationContainer.style.display = 'flex';

    // تحديث معلومات الصفحة
    const pageInfo = document.getElementById('pageInfoVacations');
    if (pageInfo) {
      const startItem = (currentPageVacations - 1) * itemsPerPageVacations + 1;
      const endItem = Math.min(currentPageVacations * itemsPerPageVacations, totalVacations);
      pageInfo.textContent = `عرض ${startItem} - ${endItem} من أصل ${totalVacations} موظف`;
    }

    // تحديث أزرار التنقل
    const prevBtn = document.getElementById('prevPageVacations');
    const nextBtn = document.getElementById('nextPageVacations');

    if (prevBtn) prevBtn.disabled = currentPageVacations <= 1;
    if (nextBtn) nextBtn.disabled = currentPageVacations >= totalPages;

    // تحديث أرقام الصفحات
    updatePageNumbersVacations(totalPages);
  }

  function updatePageNumbersVacations(totalPages) {
    const pageNumbersContainer = document.getElementById('pageNumbersVacations');
    if (!pageNumbersContainer) return;

    pageNumbersContainer.innerHTML = '';

    // إظهار رقم الصفحة حتى لو كانت صفحة واحدة فقط
    if (totalPages === 0) return;

    // حساب نطاق الصفحات المعروضة
    let startPage = Math.max(1, currentPageVacations - 2);
    let endPage = Math.min(totalPages, currentPageVacations + 2);

    // التأكد من عرض 5 صفحات على الأقل إذا كان ذلك ممكناً
    if (endPage - startPage < 4) {
      if (startPage === 1) {
        endPage = Math.min(totalPages, startPage + 4);
      } else if (endPage === totalPages) {
        startPage = Math.max(1, endPage - 4);
      }
    }

    // إضافة الصفحة الأولى إذا لم تكن في النطاق
    if (startPage > 1) {
      addPageButtonVacations(1);
      if (startPage > 2) {
        const dots = document.createElement('span');
        dots.textContent = '...';
        dots.className = 'pagination-dots';
        pageNumbersContainer.appendChild(dots);
      }
    }

    // إضافة أرقام الصفحات في النطاق
    for (let i = startPage; i <= endPage; i++) {
      addPageButtonVacations(i);
    }

    // إضافة الصفحة الأخيرة إذا لم تكن في النطاق
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        const dots = document.createElement('span');
        dots.textContent = '...';
        dots.className = 'pagination-dots';
        pageNumbersContainer.appendChild(dots);
      }
      addPageButtonVacations(totalPages);
    }
  }

  function addPageButtonVacations(pageNumber) {
    const pageNumbersContainer = document.getElementById('pageNumbersVacations');
    const button = document.createElement('button');
    button.textContent = pageNumber;
    button.className = 'pagination-btn page-btn';
    if (pageNumber === currentPageVacations) {
      button.classList.add('active');
    }
    button.addEventListener('click', () => goToPageVacations(pageNumber));
    pageNumbersContainer.appendChild(button);
  }

  function goToPageVacations(page) {
    const totalPages = Math.ceil(totalVacations / itemsPerPageVacations);
    if (page >= 1 && page <= totalPages) {
      currentPageVacations = page;
      // إعادة تطبيق الفلاتر الحالية للحصول على النتائج الصحيحة
      const searchEmployeeCode = document.getElementById('searchEmployeeCode')?.value || '';
      const searchEmployeeName = document.getElementById('searchEmployeeName')?.value || '';
      const searchFromDate = document.getElementById('searchFromDate')?.value || '';
      const searchToDate = document.getElementById('searchToDate')?.value || '';

      // تحديد ما إذا كان هناك فلاتر نشطة
      const hasActiveFilters = searchEmployeeCode || searchEmployeeName || searchFromDate || searchToDate;

      displayVacations(!hasActiveFilters); // showAll=true إذا لم تكن هناك فلاتر
    }
  }

  function changePageVacations(direction) {
    const newPage = currentPageVacations + direction;
    goToPageVacations(newPage);
  }

  // الحصول على النص العربي لنوع الإجازة
  function getVacationTypeText(type) {
    switch (type) {
      case 'casual':
        return 'إجازة عارضة';
      case 'permission':
        return 'غياب بإذن';
      case 'absence':
        return 'غياب بدون إذن';
      case 'annual':
        return 'إجازة سنوية';
      case 'unpaid':
        return 'إجازة بدون راتب';
      case 'sick':
        return 'إجازة مرضية';
      case 'official':
        return 'إجازات خارج الرصيد';
      default:
        return type;
    }
  }
  
  // تهيئة الصفحة
  function init() {
    console.log('بدء تهيئة الصفحة');

    // إخفاء البيانات افتراضياً عند بدء تحميل الصفحة
    if (vacationsTableBody) {
      vacationsTableBody.innerHTML = '<tr><td colspan="10" class="no-results">يرجى الضغط على زر "بحث" أو "عرض الكل" لعرض البيانات</td></tr>';
    }

    setupEventListeners();
    console.log('تم إعداد مستمعي الأحداث');
    loadData();
    console.log('تم بدء تحميل البيانات');
    updateVacationTypeFields();
    displayAddedVacations(); // عرض الإجازات المضافة

    // التحقق من التبويب المحدد من البطاقات
    checkSelectedTab();

    // تعيين التاريخ الحالي كقيمة افتراضية
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const formattedDate = `${year}-${month}-${day}`;

    // تعيين التاريخ لحقل تاريخ الإجازة
    const vacationDateElement = document.getElementById('vacationDate');
    if (vacationDateElement) vacationDateElement.value = formattedDate;
    
    // تعيين فترة البحث التلقائية (من 26 الشهر الماضي إلى 25 الشهر الحالي)
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    
    // تاريخ البداية: 26 من الشهر الماضي
    const searchStartDate = new Date(currentYear, currentMonth - 1, 26);
    const startYear = searchStartDate.getFullYear();
    const startMonth = String(searchStartDate.getMonth() + 1).padStart(2, '0');
    const startDay = String(searchStartDate.getDate()).padStart(2, '0');
    const formattedStartDate = `${startYear}-${startMonth}-${startDay}`;

    // تاريخ النهاية: 25 من الشهر الحالي
    const searchEndDate = new Date(currentYear, currentMonth, 25);
    const endYear = searchEndDate.getFullYear();
    const endMonth = String(searchEndDate.getMonth() + 1).padStart(2, '0');
    const endDay = String(searchEndDate.getDate()).padStart(2, '0');
    const formattedEndDate = `${endYear}-${endMonth}-${endDay}`;

    // تعيين التواريخ في حقول البحث
    const startDateFilter = document.getElementById('startDate');
    const endDateFilter = document.getElementById('endDate');
    if (startDateFilter) startDateFilter.value = formattedStartDate;
    if (endDateFilter) endDateFilter.value = formattedEndDate;
    
    // لا نعرض الإجازات تلقائياً - يجب على المستخدم الضغط على زر البحث أو عرض الكل
    // setTimeout(() => {
    //   displayVacations(true);
    // }, 1000); // تم تعطيل العرض التلقائي
  }

  // فحص الصلاحيات
  function hasPermission(permission) {
    try {
      const permissions = JSON.parse(localStorage.getItem('permissions') || '{}');
      const result = permissions[permission] === true;
      // إزالة console.log لتجنب التكرار المفرط
      return result;
    } catch (error) {
      console.error('خطأ في قراءة الصلاحيات:', error);
      return false;
    }
  }

  // دالة لترجمة أنواع الإجازات
  function getVacationTypeLabel(type) {
    const types = {
      'casual': 'إجازة عارضة',
      'permission': 'غياب بإذن',
      'absence': 'غياب بدون إذن',
      'annual': 'إجازة سنوية',
      'unpaid': 'إجازة بدون راتب',
      'sick': 'إجازة مرضية',
      'official': 'إجازات خارج الرصيد'
    };
    return types[type] || type;
  }







  // تعديل إجازة
  function editVacation(vacationId, vacationType, vacationDate, vacationDays, officialType) {
    // فحص صلاحية التعديل
    if (!hasPermission('edit_vacation')) {
      alert('ليس لديك صلاحية لتعديل الإجازات');
      return;
    }
    console.log('🔧 بدء تعديل الإجازة:', {
      vacationId,
      vacationType,
      vacationDate,
      vacationDays,
      officialType
    });

    // البحث عن بيانات الإجازة من الجدول لمعرفة الموظف
    const currentEmployeeCode = document.getElementById('detailsEmployeeCode').textContent;
    const currentEmployeeName = document.getElementById('detailsEmployeeFullName').textContent;
    const currentDepartment = document.getElementById('detailsEmployeeDepartment').textContent;
    
    // إغلاق نافذة التفاصيل أولاً
    const vacationDetailsModal = document.getElementById('vacationDetailsModal');
    vacationDetailsModal.style.display = 'none';
    
    // تفعيل تبويب إضافة الإجازة
    switchTab('add-vacation');
    
    // انتظار قصير للتأكد من تحميل التبويب
    setTimeout(() => {
      // ملء النموذج بالبيانات الحالية
      document.getElementById('vacationType').value = vacationType;

      // الآن التاريخ يأتي من الخادم بصيغة YYYY-MM-DD مباشرة
      console.log('📅 التاريخ المستلم من الخادم:', vacationDate);
      document.getElementById('vacationDate').value = vacationDate;
      document.getElementById('daysCount').value = vacationDays;
      
      // ملء بيانات الموظف في النظام الجديد
      const selectedEmployee = employees.find(emp => String(emp.code) === String(currentEmployeeCode));
      if (selectedEmployee) {
        employeeSearchAdd.value = `${selectedEmployee.code} - ${selectedEmployee.full_name}`;
        fillEmployeeFields(selectedEmployee);
      }
      
      if (vacationType === 'official' && officialType) {
        document.getElementById('officialHoliday').value = officialType;
      }
      
      // إظهار/إخفاء حقول الإجازة خارج الرصيد
      updateVacationTypeFields();
      
      // تغيير النص على زر الحفظ
      const saveBtn = document.getElementById('saveVacation');
      saveBtn.textContent = 'تحديث الإجازة';
      saveBtn.setAttribute('data-edit-id', vacationId);
      
      alert('تم تحميل بيانات الإجازة للتعديل');
    }, 300);
  }

  // تحديث إجازة موجودة
  async function updateVacation(vacationId, vacationData) {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/api/vacations/${vacationId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(vacationData)
      });
      
      if (response.ok) {
        alert('تم تحديث الإجازة بنجاح');
        
        // تحديث الإجازة في مصفوفة الإجازات المضافة إذا كانت موجودة
        const existingIndex = addedVacations.findIndex(v => v.id === vacationId);
        if (existingIndex !== -1) {
          // الحصول على بيانات الموظف من الإجازة الموجودة
          const existingVacation = addedVacations[existingIndex];
          
          // تحديث الإجازة مع الحفاظ على بيانات الموظف
          addedVacations[existingIndex] = {
            ...existingVacation,
            ...vacationData,
            id: vacationId
          };
          
          // تحديث العرض
          displayAddedVacations();
        }
        
        await loadVacations();
        resetVacationForm();
      } else if (response.status === 403) {
        // التوكن منتهي الصلاحية أو غير صالح
        handleTokenExpired();
      } else {
        const errorData = await response.json();
        alert(`فشل في تحديث الإجازة: ${errorData.error || 'خطأ غير معروف'}`);
      }
    } catch (error) {
      console.error('Error updating vacation:', error);
      alert('حدث خطأ أثناء تحديث الإجازة');
    }
  }

  // حذف إجازة
  async function deleteVacation(vacationId) {
    // فحص صلاحية الحذف
    if (!hasPermission('delete_vacation')) {
      alert('ليس لديك صلاحية لحذف الإجازات');
      return;
    }

    try {
      if (!vacationId) {
        console.error('معرف الإجازة غير موجود');
        alert('خطأ: معرف الإجازة غير موجود');
        return;
      }
      
      console.log(`جاري حذف الإجازة رقم: ${vacationId}`);
      
      // التأكد من أن عنوان API صحيح
      const deleteUrl = `${API_URL}/api/vacations/${vacationId}`;
      console.log(`عنوان الحذف: ${deleteUrl}`);
      
      const token = localStorage.getItem('token');
      const response = await fetch(deleteUrl, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log(`استجابة الخادم: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        alert('تم حذف الإجازة بنجاح');
        // إعادة تحميل الإجازات وإغلاق النافذة المنبثقة
        await loadVacations();
        vacationDetailsModal.style.display = 'none';
      } else if (response.status === 403) {
        // التوكن منتهي الصلاحية أو غير صالح
        handleTokenExpired();
      } else {
        let errorMessage = 'خطأ غير معروف';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || 'خطأ غير معروف';
        } catch (jsonError) {
          console.error('خطأ في تحليل استجابة الخطأ:', jsonError);
        }
        alert(`فشل في حذف الإجازة: ${errorMessage}`);
      }
    } catch (error) {
      console.error('Error deleting vacation:', error);
      alert(`حدث خطأ أثناء حذف الإجازة: ${error.message}`);
    }
  }

  // حذف جميع الإجازات
  async function clearAllVacations() {
    // طلب كلمة تأكيد من المستخدم
    const confirmWord = prompt('لتأكيد حذف جميع الإجازات، اكتب كلمة "تأكيد"');
    
    // التحقق من كلمة التأكيد
    if (confirmWord !== 'تأكيد') {
      alert('لم يتم حذف الإجازات. يجب كتابة كلمة "تأكيد" للمتابعة.');
      return;
    }
    
    try {
      const response = await fetch(`${API_URL}/api/vacations`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        alert('تم حذف جميع الإجازات بنجاح');
        // إعادة تحميل الإجازات وإخفاء البيانات
        await loadVacations();
        vacationsTableBody.innerHTML = '<tr><td colspan="10" class="no-results">يرجى الضغط على زر "بحث" أو "عرض الكل" لعرض البيانات</td></tr>';
      } else {
        const errorData = await response.json();
        alert(`فشل في حذف الإجازات: ${errorData.message || 'خطأ غير معروف'}`);
      }
    } catch (error) {
      console.error('خطأ في حذف الإجازات:', error);
      alert('حدث خطأ أثناء محاولة حذف الإجازات');
    }
  }

  // فتح النافذة المنبثقة لتعديل الإجازة
  function openEditVacationModal(vacation, index) {
    const modal = document.getElementById('editAddedVacationModal');

    if (!modal) {
      console.error('لم يتم العثور على نافذة التعديل');
      alert('خطأ: لم يتم العثور على نافذة التعديل');
      return;
    }

    console.log('فتح نافذة التعديل للإجازة:', vacation);
    console.log('بيانات الإجازة الكاملة:', JSON.stringify(vacation, null, 2));

    // ملء النموذج بالبيانات الحالية
    document.getElementById('editVacationId').value = vacation.id || '';
    document.getElementById('editVacationEmployeeCode').value = vacation.employee_code || '';
    document.getElementById('editVacationEmployeeName').value = vacation.employee_name || '';
    document.getElementById('editVacationEmployeeDepartment').value = vacation.department || '';

    // تحويل نوع الإجازة إلى القيم المتوقعة في النموذج
    let vacationTypeValue = vacation.vacation_type;

    // تحويل القيم الإنجليزية إلى العربية إذا لزم الأمر
    const typeMapping = {
      'casual': 'عارضة',
      'permission': 'غياب بإذن',
      'absence': 'غياب بدون إذن',
      'annual': 'سنوية',
      'unpaid': 'بدون راتب',
      'sick': 'مرضي',
      'official': 'إجازات خارج الرصيد'
    };

    if (typeMapping[vacationTypeValue]) {
      vacationTypeValue = typeMapping[vacationTypeValue];
    }

    // التأكد من أن القيمة موجودة في قائمة الخيارات
    const selectElement = document.getElementById('editVacationType');
    const options = Array.from(selectElement.options).map(option => option.value);
    if (!options.includes(vacationTypeValue)) {
      console.warn('نوع الإجازة غير موجود في القائمة:', vacationTypeValue);
      // استخدام القيمة الأصلية إذا لم تكن القيمة المحولة موجودة
      vacationTypeValue = vacation.vacation_type;
    }

    document.getElementById('editVacationType').value = vacationTypeValue;

    // تعيين تاريخ الإجازة
    const editVacationDate = document.getElementById('editVacationDate');
    console.log('تاريخ الإجازة الأصلي:', vacation.vacation_date);

    // تنسيق التاريخ للعرض في input date
    let formattedDate = vacation.vacation_date || '';
    if (formattedDate && !formattedDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
      // إذا كان التاريخ بتنسيق مختلف، حاول تحويله
      const dateObj = new Date(formattedDate);
      if (!isNaN(dateObj.getTime())) {
        formattedDate = dateObj.toISOString().split('T')[0];
      }
    }

    console.log('التاريخ المنسق:', formattedDate);
    editVacationDate.value = formattedDate;

    // التعامل مع الإجازة خارج الرصيد
    const editOfficialHolidayGroup = document.getElementById('editOfficialHolidayGroup');

    if (editOfficialHolidayGroup) {
      if (vacationTypeValue === 'إجازات خارج الرصيد' || vacation.vacation_type === 'official') {
        editOfficialHolidayGroup.style.display = 'block';

        // تحويل نوع الإجازة إذا لزم الأمر
        let officialType = vacation.official_type || '';
        const officialTypeMapping = {
          // الإجازات خارج الرصيد الموجودة
          'police_day': 'عيد الشرطة (25 يناير)',
          'sinai_day': 'عيد تحرير سيناء',
          'labor_day': 'عيد العمال',
          'june_revolution': 'عيد ثورة 30 يونيو',
          'july_revolution': 'عيد ثورة 23 يوليو',
          'armed_forces_day': 'عيد القوات المسلحة (6 أكتوبر)',
          'christmas': 'عيد الميلاد المجيد',
          'sham_el_nessim': 'عيد شم النسيم',
          'islamic_new_year': 'رأس السنة الهجرية',
          'prophet_birthday': 'المولد النبوي الشريف',
          'eid_adha': 'عيد الأضحى',
          'eid_fitr': 'عيد الفطر',
          // الأنواع الجديدة المضافة بعد عيد الفطر
          'emergency': 'اجازة اضطرارية',
          'birth': 'مولود',
          'maternity': 'اجازة وضع',
          'marriage': 'زواج',
          'death_first_degree': 'الوفاة من الدرجة الاولى',
          'military_service': 'الاستدعاء للجيش',
          'exams': 'الامتحانات',
          'pilgrimage': 'الحج والعمرة',
          // الأنواع القديمة للتوافق مع البيانات الموجودة
          'new_year': 'رأس السنة الميلادية',
          'coptic_christmas': 'عيد الميلاد المجيد',
          'january_revolution': 'ثورة 25 يناير',
          'sinai_liberation': 'تحرير سيناء',
          'eid_al_adha': 'عيد الأضحى المبارك',
          'eid_al_fitr': 'عيد الفطر المبارك',
          'prophet_birthday': 'المولد النبوي'
        };

        if (officialTypeMapping[officialType]) {
          officialType = officialTypeMapping[officialType];
        }

        const editOfficialHoliday = document.getElementById('editOfficialHoliday');
        if (editOfficialHoliday) {
          editOfficialHoliday.value = officialType;
        }
      } else {
        editOfficialHolidayGroup.style.display = 'none';
      }
    }

    // حفظ الفهرس للاستخدام عند التحديث
    modal.setAttribute('data-vacation-index', index);

    // إعداد event listeners للنافذة إذا لم تكن مُعدة من قبل
    if (!modal.hasAttribute('data-listeners-setup')) {
      setupEditVacationModalListeners();
      modal.setAttribute('data-listeners-setup', 'true');
    }

    // عرض النافذة المنبثقة
    modal.style.display = 'block';

    console.log('تم ملء النموذج بالبيانات:', {
      id: vacation.id,
      employee_code: vacation.employee_code,
      vacation_type: vacationTypeValue,
      vacation_date: vacation.vacation_date
    });
  }

  // إعداد event listeners لنافذة تعديل الإجازة
  function setupEditVacationModalListeners() {
    const modal = document.getElementById('editAddedVacationModal');
    const closeBtn = document.getElementById('closeEditAddedVacationModal');
    const updateBtn = document.getElementById('updateVacationBtn');
    const cancelBtn = document.getElementById('cancelEditVacationBtn');
    const editVacationType = document.getElementById('editVacationType');

    if (!modal || !closeBtn || !updateBtn || !editVacationType) {
      console.error('لم يتم العثور على عناصر نافذة التعديل');
      return;
    }

    // إغلاق النافذة عند النقر على X
    closeBtn.addEventListener('click', () => {
      modal.style.display = 'none';
    });

    // إغلاق النافذة عند النقر على زر الإلغاء
    if (cancelBtn) {
      cancelBtn.addEventListener('click', () => {
        modal.style.display = 'none';
      });
    }

    // إغلاق النافذة عند النقر خارجها
    window.addEventListener('click', (event) => {
      if (event.target === modal) {
        modal.style.display = 'none';
      }
    });

    // إظهار/إخفاء حقل الإجازة خارج الرصيد
    editVacationType.addEventListener('change', () => {
      const officialGroup = document.getElementById('editOfficialHolidayGroup');
      if (officialGroup) {
        if (editVacationType.value === 'إجازات خارج الرصيد') {
          officialGroup.style.display = 'block';
        } else {
          officialGroup.style.display = 'none';
        }
      }
    });

    // معالج تحديث الإجازة
    updateBtn.addEventListener('click', async () => {
      await updateVacationFromModal();
    });
  }

  // تحديث الإجازة من النافذة المنبثقة
  async function updateVacationFromModal() {
    try {
      const modal = document.getElementById('editAddedVacationModal');
      const vacationIndex = parseInt(modal.getAttribute('data-vacation-index'));

      if (isNaN(vacationIndex) || vacationIndex < 0 || vacationIndex >= addedVacations.length) {
        alert('خطأ: لم يتم العثور على الإجازة المحددة');
        return;
      }

      const vacation = addedVacations[vacationIndex];

      // جمع البيانات من النموذج
      const formVacationType = document.getElementById('editVacationType').value;
      const formVacationDate = document.getElementById('editVacationDate').value;
      const formOfficialType = document.getElementById('editOfficialHoliday').value;

      // التحقق من صحة البيانات
      if (!formVacationType || !formVacationDate) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
      }

      if (formVacationType === 'إجازات خارج الرصيد' && !formOfficialType) {
        alert('يرجى اختيار نوع الإجازة');
        return;
      }

      // تحويل نوع الإجازة إلى القيم المطلوبة في قاعدة البيانات
      const typeMapping = {
        'عارضة': 'casual',
        'غياب بإذن': 'permission',
        'غياب بدون إذن': 'absence',
        'سنوية': 'annual',
        'بدون راتب': 'unpaid',
        'مرضي': 'sick',
        'إجازات خارج الرصيد': 'official'
      };

      const dbVacationType = typeMapping[formVacationType] || formVacationType;

      // تحويل نوع الإجازة إلى القيم المطلوبة في قاعدة البيانات
      let dbOfficialType = null;
      if (formVacationType === 'إجازات خارج الرصيد') {
        const officialTypeMapping = {
          // الإجازات خارج الرصيد الموجودة
          'عيد الشرطة (25 يناير)': 'police_day',
          'عيد تحرير سيناء': 'sinai_day',
          'عيد العمال': 'labor_day',
          'عيد ثورة 30 يونيو': 'june_revolution',
          'عيد ثورة 23 يوليو': 'july_revolution',
          'عيد القوات المسلحة (6 أكتوبر)': 'armed_forces_day',
          'عيد الميلاد المجيد': 'christmas',
          'عيد شم النسيم': 'sham_el_nessim',
          'رأس السنة الهجرية': 'islamic_new_year',
          'المولد النبوي الشريف': 'prophet_birthday',
          'عيد الأضحى': 'eid_adha',
          'عيد الفطر': 'eid_fitr',
          // الأنواع الجديدة المضافة بعد عيد الفطر
          'اجازة اضطرارية': 'emergency',
          'مولود': 'birth',
          'اجازة وضع': 'maternity',
          'زواج': 'marriage',
          'الوفاة من الدرجة الاولى': 'death_first_degree',
          'الاستدعاء للجيش': 'military_service',
          'الامتحانات': 'exams',
          'الحج والعمرة': 'pilgrimage',
          // الأنواع القديمة للتوافق مع البيانات الموجودة
          'رأس السنة الميلادية': 'new_year',
          'ثورة 25 يناير': 'january_revolution',
          'تحرير سيناء': 'sinai_liberation',
          'شم النسيم': 'sham_el_nessim',
          'ثورة 23 يوليو': 'july_revolution',
          'المولد النبوي': 'prophet_birthday',
          'eid_al_fitr': 'eid_al_fitr',
          'eid_al_adha': 'eid_al_adha'
        };
        dbOfficialType = officialTypeMapping[formOfficialType] || formOfficialType;
      }

      const updatedVacation = {
        employee_code: document.getElementById('editVacationEmployeeCode').value,
        employee_name: document.getElementById('editVacationEmployeeName').value,
        department: document.getElementById('editVacationEmployeeDepartment').value,
        vacation_type: dbVacationType,
        vacation_date: formVacationDate,
        official_type: dbOfficialType
      };

      // إرسال التحديث إلى الخادم
      await updateVacationOnServer(vacation.id, updatedVacation, vacationIndex, formVacationType, formOfficialType);
    } catch (error) {
      console.error('خطأ في updateVacationFromModal:', error);
      alert('حدث خطأ أثناء تحديث الإجازة: ' + error.message);
    }
  }

  // تحديث الإجازة على الخادم
  async function updateVacationOnServer(vacationId, updatedVacation, vacationIndex, formVacationType, formOfficialType) {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/api/vacations/${vacationId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(updatedVacation)
      });

      if (response.ok) {
        // محاولة قراءة الاستجابة كـ JSON إذا كانت متوفرة
        let responseData = null;
        try {
          const responseText = await response.text();
          if (responseText) {
            responseData = JSON.parse(responseText);
          }
        } catch (parseError) {
          console.log('لا توجد بيانات JSON في الاستجابة، وهذا طبيعي');
        }

        // تحديث الإجازة في المصفوفة المحلية
        addedVacations[vacationIndex] = {
          ...addedVacations[vacationIndex],
          employee_code: updatedVacation.employee_code,
          employee_name: updatedVacation.employee_name,
          department: updatedVacation.department,
          vacation_type: formVacationType, // استخدام القيمة العربية للعرض
          vacation_date: updatedVacation.vacation_date,
          official_type: formOfficialType // استخدام القيمة العربية للعرض
        };

        // تحديث العرض
        displayAddedVacations();

        // إغلاق النافذة
        document.getElementById('editAddedVacationModal').style.display = 'none';

        alert('تم تحديث الإجازة بنجاح');
      } else {
        // معالجة الأخطاء
        let errorMessage = `خطأ HTTP ${response.status}: ${response.statusText}`;
        try {
          const responseText = await response.text();
          if (responseText) {
            const errorData = JSON.parse(responseText);
            errorMessage = errorData.error || errorData.message || errorMessage;
          }
        } catch (parseError) {
          console.log('لا يمكن تحليل رسالة الخطأ كـ JSON');
        }
        alert(`خطأ في تحديث الإجازة: ${errorMessage}`);
      }
    } catch (error) {
      console.error('خطأ في تحديث الإجازة:', error);
      alert('حدث خطأ في تحديث الإجازة');
    }
  }

  // ===== وظائف التقارير =====
  
  // متغيرات التقارير
  let currentReportTab = 'employee-summary';
  
  // تهيئة التقارير
  function initializeReports() {
    // إضافة مستمعي الأحداث لتبويبات التقارير
    const reportTabBtns = document.querySelectorAll('.report-tab-btn');
    reportTabBtns.forEach(btn => {
      btn.addEventListener('click', function() {
        const reportType = this.getAttribute('data-report');
        switchReportTab(reportType);
      });
    });
    
    // إضافة مستمعي الأحداث لأزرار التقارير
    setupReportEventListeners();
    
    // تحميل السنوات في القوائم المنسدلة
    populateYearSelects();
    
    // تحميل الإدارات في قوائم التقارير
    populateReportDepartments();
    
    // تحميل اقتراحات الموظفين للتقرير المخصص
    setupCustomEmployeeSearch();
  }
  
  // تبديل تبويبات التقارير
  function switchReportTab(reportType) {
    // إخفاء جميع محتويات التقارير
    const reportContents = document.querySelectorAll('.report-content');
    reportContents.forEach(content => {
      content.style.display = 'none';
    });
    
    // إزالة الفئة النشطة من جميع الأزرار
    const reportTabBtns = document.querySelectorAll('.report-tab-btn');
    reportTabBtns.forEach(btn => {
      btn.classList.remove('active');
    });
    
    // إظهار المحتوى المحدد وتفعيل الزر
    document.getElementById(reportType).style.display = 'block';
    document.querySelector(`[data-report="${reportType}"]`).classList.add('active');
    
    currentReportTab = reportType;
  }
  
  // إعداد مستمعي أحداث التقارير
  function setupReportEventListeners() {
    console.log('إعداد مستمعي أحداث التقارير...');

    // تقرير إجمالي الإجازات حسب الموظف
    const generateEmployeeSummaryBtn = document.getElementById('generateEmployeeSummaryBtn');
    const exportEmployeeSummaryBtn = document.getElementById('exportEmployeeSummaryBtn');

    console.log('زر تقرير إجمالي الإجازات:', generateEmployeeSummaryBtn ? 'موجود' : 'غير موجود');
    console.log('زر تصدير تقرير إجمالي الإجازات:', exportEmployeeSummaryBtn ? 'موجود' : 'غير موجود');

    if (generateEmployeeSummaryBtn) {
      generateEmployeeSummaryBtn.addEventListener('click', generateEmployeeSummaryReport);
      console.log('تم إضافة مستمع الحدث لزر تقرير إجمالي الإجازات');
    }
    if (exportEmployeeSummaryBtn) {
      exportEmployeeSummaryBtn.addEventListener('click', exportEmployeeSummaryReport);
      console.log('تم إضافة مستمع الحدث لزر تصدير تقرير إجمالي الإجازات');
    }
    
    // تقرير الإجازات حسب الإدارة
    const generateDeptSummaryBtn = document.getElementById('generateDeptSummaryBtn');
    const exportDeptSummaryBtn = document.getElementById('exportDeptSummaryBtn');
    console.log('زر تقرير الإدارة:', generateDeptSummaryBtn ? 'موجود' : 'غير موجود');
    if (generateDeptSummaryBtn) generateDeptSummaryBtn.addEventListener('click', generateDepartmentSummaryReport);
    if (exportDeptSummaryBtn) exportDeptSummaryBtn.addEventListener('click', exportDepartmentSummaryReport);

    // تقرير الغياب المتكرر
    const generateAbsenceReportBtn = document.getElementById('generateAbsenceReportBtn');
    const exportAbsenceReportBtn = document.getElementById('exportAbsenceReportBtn');
    console.log('زر تقرير الغياب المتكرر:', generateAbsenceReportBtn ? 'موجود' : 'غير موجود');
    if (generateAbsenceReportBtn) generateAbsenceReportBtn.addEventListener('click', generateAbsenceReport);
    if (exportAbsenceReportBtn) exportAbsenceReportBtn.addEventListener('click', exportAbsenceReport);

    // تقرير الأيام الأعلى غيابًا
    const generateAbsenceDaysReportBtn = document.getElementById('generateAbsenceDaysReportBtn');
    const exportAbsenceDaysReportBtn = document.getElementById('exportAbsenceDaysReportBtn');
    console.log('زر تقرير الأيام الأعلى غياباً:', generateAbsenceDaysReportBtn ? 'موجود' : 'غير موجود');
    if (generateAbsenceDaysReportBtn) generateAbsenceDaysReportBtn.addEventListener('click', generateHighestAbsenceDaysReport);
    if (exportAbsenceDaysReportBtn) exportAbsenceDaysReportBtn.addEventListener('click', exportHighestAbsenceDaysReport);

    // تقرير الموظفين الأعلى استخدامًا للإجازات
    const generateTopUsersReportBtn = document.getElementById('generateTopUsersReportBtn');
    const exportTopUsersReportBtn = document.getElementById('exportTopUsersReportBtn');
    console.log('زر تقرير الموظفين الأعلى استخداماً:', generateTopUsersReportBtn ? 'موجود' : 'غير موجود');
    if (generateTopUsersReportBtn) generateTopUsersReportBtn.addEventListener('click', generateTopVacationUsersReport);
    if (exportTopUsersReportBtn) exportTopUsersReportBtn.addEventListener('click', exportTopVacationUsersReport);

    // التقرير المخصص للموظف
    const generateCustomEmployeeBtn = document.getElementById('generateCustomEmployeeBtn');
    const exportCustomEmployeeBtn = document.getElementById('exportCustomEmployeeBtn');
    const printCustomEmployeeBtn = document.getElementById('printCustomEmployeeBtn');
    console.log('زر التقرير المخصص:', generateCustomEmployeeBtn ? 'موجود' : 'غير موجود');
    if (generateCustomEmployeeBtn) generateCustomEmployeeBtn.addEventListener('click', generateCustomEmployeeReport);
    if (exportCustomEmployeeBtn) exportCustomEmployeeBtn.addEventListener('click', exportCustomEmployeeReport);
    if (printCustomEmployeeBtn) printCustomEmployeeBtn.addEventListener('click', printCustomEmployeeReport);

    console.log('تم الانتهاء من إعداد جميع مستمعي أحداث التقارير');
  }
  
  // تحميل السنوات في القوائم المنسدلة
  function populateYearSelects() {
    const currentYear = new Date().getFullYear();
    const yearSelects = ['employeeSummaryYear', 'customEmployeeYear'];
    
    yearSelects.forEach(selectId => {
      const select = document.getElementById(selectId);
      if (select) {
        // إضافة السنوات من 2020 إلى السنة الحالية + 1
        for (let year = 2020; year <= currentYear + 1; year++) {
          const option = document.createElement('option');
          option.value = year;
          option.textContent = year;
          if (year === currentYear) {
            option.selected = true;
          }
          select.appendChild(option);
        }
      }
    });
  }
  
  // تحميل الإدارات في قوائم التقارير
  function populateReportDepartments() {
    console.log('تحميل الإدارات في قوائم التقارير:', departments);
    const deptSelect = document.getElementById('employeeSummaryDept');
    const absenceDeptSelect = document.getElementById('absenceDepartment');

    if (deptSelect && departments.length > 0) {
      deptSelect.innerHTML = '<option value="">كل الإدارات</option>';
      departments.forEach(dept => {
        const option = document.createElement('option');
        option.value = dept;
        option.textContent = dept;
        option.style.color = '#000';
        option.style.backgroundColor = 'white';
        deptSelect.appendChild(option);
      });
    }

    if (absenceDeptSelect && departments.length > 0) {
      absenceDeptSelect.innerHTML = '<option value="">كل الإدارات</option>';
      departments.forEach(dept => {
        const option = document.createElement('option');
        option.value = dept;
        option.textContent = dept;
        option.style.color = '#000';
        option.style.backgroundColor = 'white';
        absenceDeptSelect.appendChild(option);
      });
    }

    // تحديث قائمة الإدارات في تقرير الموظفين الأعلى استخدامًا للإجازات
    const topUsersDeptSelect = document.getElementById('topUsersDepartment');
    if (topUsersDeptSelect && departments.length > 0) {
      topUsersDeptSelect.innerHTML = '<option value="">كل الإدارات</option>';
      departments.forEach(dept => {
        const option = document.createElement('option');
        option.value = dept;
        option.textContent = dept;
        option.style.color = '#000';
        option.style.backgroundColor = 'white';
        topUsersDeptSelect.appendChild(option);
      });
    }
  }
  
  // إعداد البحث عن الموظف في التقرير المخصص
  function setupCustomEmployeeSearch() {
    const searchInput = document.getElementById('customEmployeeSearch');
    const suggestions = document.getElementById('customEmployeeSuggestions');
    
    if (searchInput && suggestions) {
      searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        suggestions.innerHTML = '';
        
        if (searchTerm.length >= 2) {
          const filteredEmployees = employees.filter(emp => {
            const empName = (emp.full_name || emp.name || '').toLowerCase();
            const empCode = (emp.code || '').toString().toLowerCase();
            return empName.includes(searchTerm) || empCode.includes(searchTerm);
          });

          filteredEmployees.slice(0, 10).forEach(emp => {
            const option = document.createElement('option');
            const empCode = emp.code || 'غير محدد';
            const empName = emp.full_name || emp.name || 'غير محدد';
            option.value = `${empCode} - ${empName}`;
            suggestions.appendChild(option);
          });
        }
      });
    }
  }
  
  // تقرير إجمالي الإجازات حسب الموظف
  async function generateEmployeeSummaryReport() {
    console.log('🔥 تم الضغط على زر تقرير إجمالي الإجازات حسب الموظف');
    try {
      const department = document.getElementById('employeeSummaryDept').value;
      const year = document.getElementById('employeeSummaryYear').value;
      const startDate = document.getElementById('employeeSummaryStartDate').value;
      const endDate = document.getElementById('employeeSummaryEndDate').value;
      
      showLoading('جاري إنشاء التقرير...');
      
      // التحقق من وجود البيانات
      if (!employees || employees.length === 0) {
        alert('لا توجد بيانات موظفين. يرجى التأكد من تحميل بيانات الموظفين أولاً.');
        hideLoading();
        return;
      }

      if (!vacations || vacations.length === 0) {
        alert('لا توجد بيانات إجازات. يرجى إضافة بعض الإجازات أولاً.');
        hideLoading();
        return;
      }
      
      // فلترة الموظفين حسب الإدارة
      let filteredEmployees = [...employees];
      if (department) {
        filteredEmployees = employees.filter(emp => emp.department === department);
      }
      
      // فلترة الإجازات حسب المعايير
      let filteredVacations = [...vacations];
      
      if (startDate) {
        const filterStartDate = new Date(startDate);
        filterStartDate.setHours(0, 0, 0, 0);
        filteredVacations = filteredVacations.filter(vac => {
          const vacationDate = new Date(vac.vacation_date);
          vacationDate.setHours(0, 0, 0, 0);
          return vacationDate >= filterStartDate;
        });
      }
      
      if (endDate) {
        const filterEndDate = new Date(endDate);
        filterEndDate.setHours(23, 59, 59, 999);
        filteredVacations = filteredVacations.filter(vac => {
          const vacationDate = new Date(vac.vacation_date);
          vacationDate.setHours(0, 0, 0, 0);
          return vacationDate <= filterEndDate;
        });
      }
      
      if (year) {
        filteredVacations = filteredVacations.filter(vac => {
          const vacYear = new Date(vac.vacation_date).getFullYear();
          return vacYear == year;
        });
      }
      
      // إنشاء تقرير لكل موظف
      const reportData = filteredEmployees.map(employee => {
        // البحث عن إجازات الموظف بطرق متعددة للتأكد من العثور على البيانات
        const empVacations = filteredVacations.filter(vac => {
          return vac.employee_code == employee.code ||
                 vac.employee_name === employee.full_name ||
                 vac.employee_name === employee.name;
        });

        const summary = {
          code: employee.code,
          name: employee.full_name || employee.name,
          department: employee.department,
          casual: 0,
          permission: 0,
          absence: 0,
          annual: 0,
          unpaid: 0,
          sick: 0,
          official: 0,
          total: 0
        };
        
        // فحص وجود إجازات من العام المالي السابق
        let hasPreviousYearVacations = false;

        empVacations.forEach(vac => {
          // فحص إذا كانت الإجازة من العام المالي السابق
          if (isBeforeJune26(vac.vacation_date)) {
            hasPreviousYearVacations = true;
          }

          // استخدام عدد الأيام الفعلي من قاعدة البيانات
          const days = parseInt(vac.days_count) || 1;
          const vacType = vac.vacation_type || vac.type;
          switch(vacType) {
            case 'casual':
            case 'عارضة':
            case 'إجازة عارضة':
              summary.casual += days;
              break;
            case 'annual':
            case 'سنوية':
            case 'إجازة سنوية':
              summary.annual += days;
              break;
            case 'permission':
            case 'غياب بإذن':
            case 'إجازة بإذن':
              summary.permission += days;
              break;
            case 'absence':
            case 'غياب بدون إذن':
            case 'إجازة بدون إذن':
              summary.absence += days;
              break;
            case 'sick':
            case 'مرضي':
            case 'إجازة مرضية':
              summary.sick += days;
              break;
            case 'unpaid':
            case 'بدون راتب':
            case 'إجازة بدون راتب':
              summary.unpaid += days;
              break;
            case 'official':
            case 'إجازات خارج الرصيد':
            case 'إجازة رسمية': // للتوافق مع البيانات القديمة
              summary.official += days;
              break;
          }
          summary.total += days;
        });

        // إضافة معلومة وجود إجازات من العام السابق
        summary.hasPreviousYearVacations = hasPreviousYearVacations;

        // إضافة الإجازات المفلترة للموظف
        summary.filteredVacations = empVacations;

        return summary;
      });
      
      // عرض البيانات في الجدول
      displayEmployeeSummaryReport(reportData);
      hideLoading();
      
    } catch (error) {
      console.error('خطأ في إنشاء تقرير إجمالي الإجازات:', error);
      hideLoading();
      alert('حدث خطأ في إنشاء التقرير');
    }
  }
  
  // عرض تقرير إجمالي الإجازات
  function displayEmployeeSummaryReport(data) {
    const tbody = document.getElementById('employeeSummaryTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    if (!data || data.length === 0) {
      tbody.innerHTML = '<tr><td colspan="12" class="no-results">لا توجد بيانات لعرضها</td></tr>';
      return;
    }

    data.forEach(row => {
      const tr = document.createElement('tr');
      tr.innerHTML = `
        <td>${row.code || 'غير محدد'}</td>
        <td>${row.name || 'غير محدد'}</td>
        <td>${row.department || 'غير محدد'}</td>
        <td>${row.casual || 0}</td>
        <td>${row.annual || 0}</td>
        <td>${row.permission || 0}</td>
        <td>${row.absence || 0}</td>
        <td>${row.sick || 0}</td>
        <td>${row.unpaid || 0}</td>
        <td>${row.official || 0}</td>
        <td><strong>${row.total || 0}</strong></td>
        <td><button class="details-btn" data-employee-code="${row.code}" data-employee-name="${row.name}">عرض التفاصيل</button></td>
      `;

      // التحقق من وجود إجازات من العام المالي السابق
      if (row.hasPreviousYearVacations) {
        tr.style.backgroundColor = '#ffebee'; // خلفية حمراء فاتحة
        tr.style.color = '#c62828'; // نص أحمر داكن
        tr.title = 'يحتوي على إجازات من العام المالي السابق (قبل 26-6)';
      }

      // إضافة event listener لزر التفاصيل
      const detailsBtn = tr.querySelector('.details-btn');
      detailsBtn.addEventListener('click', async () => {
        await showEmployeeVacationDetails(row.code, row.name, row.filteredVacations);
      });

      tbody.appendChild(tr);
    });
  }
  
  // تقرير الإجازات حسب الإدارة
  async function generateDepartmentSummaryReport() {
    try {
      const startDate = document.getElementById('deptSummaryStartDate').value;
      const endDate = document.getElementById('deptSummaryEndDate').value;
      const vacationType = document.getElementById('deptSummaryVacationType').value;
      
      showLoading('جاري إنشاء التقرير...');
      
      // التحقق من وجود البيانات
      if (!employees || employees.length === 0) {
        alert('لا توجد بيانات موظفين. يرجى التأكد من تحميل بيانات الموظفين أولاً.');
        hideLoading();
        return;
      }

      if (!vacations || vacations.length === 0) {
        alert('لا توجد بيانات إجازات. يرجى إضافة بعض الإجازات أولاً.');
        hideLoading();
        return;
      }
      
      // فلترة الإجازات
      let filteredVacations = [...vacations];
      
      if (startDate) {
        const filterStartDate = new Date(startDate);
        filterStartDate.setHours(0, 0, 0, 0);
        filteredVacations = filteredVacations.filter(vac => {
          const vacationDate = new Date(vac.vacation_date);
          vacationDate.setHours(0, 0, 0, 0);
          return vacationDate >= filterStartDate;
        });
      }
      
      if (endDate) {
        const filterEndDate = new Date(endDate);
        filterEndDate.setHours(23, 59, 59, 999);
        filteredVacations = filteredVacations.filter(vac => {
          const vacationDate = new Date(vac.vacation_date);
          vacationDate.setHours(0, 0, 0, 0);
          return vacationDate <= filterEndDate;
        });
      }
      
      if (vacationType) {
        filteredVacations = filteredVacations.filter(vac => {
          const vacType = vac.vacation_type || vac.type;
          return vacType === vacationType;
        });
      }
      
      // تجميع البيانات حسب الإدارة
      const deptData = {};
      
      // إنشاء قائمة الإدارات من بيانات الموظفين
      const uniqueDepartments = [...new Set(employees.map(emp => emp.department).filter(dept => dept))];
      
      uniqueDepartments.forEach(deptName => {
        deptData[deptName] = {
          department: deptName,
          employeeCount: 0,
          vacationCount: 0,
          totalDays: 0,
          avgDays: 0
        };
      });
      
      // حساب عدد الموظفين في كل إدارة
      employees.forEach(emp => {
        const deptName = emp.department;
        if (deptName && deptData[deptName]) {
          deptData[deptName].employeeCount++;
        }
      });
      
      // حساب الإجازات والأيام
      filteredVacations.forEach(vac => {
        // البحث عن الموظف بطرق متعددة
        const employee = employees.find(emp => {
          return emp.code == vac.employee_code ||
                 emp.full_name === vac.employee_name ||
                 emp.name === vac.employee_name;
        });
        
        if (employee && employee.department && deptData[employee.department]) {
          deptData[employee.department].vacationCount++;
          const days = parseInt(vac.days_count) || 1;
          deptData[employee.department].totalDays += days;
        }
      });
      
      // حساب المتوسط
      Object.values(deptData).forEach(dept => {
        if (dept.employeeCount > 0) {
          dept.avgDays = (dept.totalDays / dept.employeeCount).toFixed(2);
        }
      });
      
      displayDepartmentSummaryReport(Object.values(deptData));
      hideLoading();
      
    } catch (error) {
      console.error('خطأ في إنشاء تقرير الإدارات:', error);
      hideLoading();
      alert('حدث خطأ في إنشاء التقرير');
    }
  }
  
  // عرض تقرير الإدارات
  function displayDepartmentSummaryReport(data) {
    const tbody = document.getElementById('deptSummaryTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    if (!data || data.length === 0) {
      tbody.innerHTML = '<tr><td colspan="5" class="no-results">لا توجد بيانات لعرضها</td></tr>';
      return;
    }

    data.forEach(row => {
      const tr = document.createElement('tr');
      tr.innerHTML = `
        <td>${row.department || 'غير محدد'}</td>
        <td>${row.employeeCount || 0}</td>
        <td>${row.vacationCount || 0}</td>
        <td>${row.totalDays || 0}</td>
        <td>${row.avgDays || '0.00'}</td>
      `;
      tbody.appendChild(tr);
    });
  }
  
  // تقرير الغياب المتكرر
  async function generateAbsenceReport() {
    try {
      const department = document.getElementById('absenceDepartment').value;
      const minDays = parseInt(document.getElementById('absenceMinDays').value) || 3;
      const startDate = document.getElementById('absenceStartDate').value;
      const endDate = document.getElementById('absenceEndDate').value;
      
      showLoading('جاري إنشاء التقرير...');
      
      // التحقق من وجود البيانات
      if (!employees || employees.length === 0) {
        alert('لا توجد بيانات موظفين. يرجى التأكد من تحميل بيانات الموظفين أولاً.');
        hideLoading();
        return;
      }

      if (!vacations || vacations.length === 0) {
        alert('لا توجد بيانات إجازات. يرجى إضافة بعض الإجازات أولاً.');
        hideLoading();
        return;
      }
      
      // فلترة إجازات الغياب بدون إذن
      let absenceVacations = vacations.filter(vac => {
        const vacType = vac.vacation_type || vac.type;
        return vacType === 'absence' || vacType === 'غياب بدون إذن' || vacType === 'إجازة بدون إذن';
      });
      
      if (startDate) {
        const filterStartDate = new Date(startDate);
        filterStartDate.setHours(0, 0, 0, 0);
        absenceVacations = absenceVacations.filter(vac => {
          const vacationDate = new Date(vac.vacation_date);
          vacationDate.setHours(0, 0, 0, 0);
          return vacationDate >= filterStartDate;
        });
      }
      
      if (endDate) {
        const filterEndDate = new Date(endDate);
        filterEndDate.setHours(23, 59, 59, 999);
        absenceVacations = absenceVacations.filter(vac => {
          const vacationDate = new Date(vac.vacation_date);
          vacationDate.setHours(0, 0, 0, 0);
          return vacationDate <= filterEndDate;
        });
      }
      
      // تجميع البيانات حسب الموظف
      const employeeAbsences = {};
      
      absenceVacations.forEach(vac => {
        // تحديد معرف الموظف
        const empKey = vac.employee_code || vac.employee_id || vac.employee_name;
        
        if (!employeeAbsences[empKey]) {
          // البحث عن الموظف بطرق متعددة
          const employee = employees.find(emp => {
            return emp.code == vac.employee_code ||
                   emp.full_name === vac.employee_name ||
                   emp.name === vac.employee_name;
          });

          employeeAbsences[empKey] = {
            code: employee ? employee.code : (vac.employee_code || empKey),
            name: employee ? (employee.full_name || employee.name) : (vac.employee_name || 'غير معروف'),
            department: employee ? employee.department : 'غير معروف',
            totalDays: 0,
            absenceCount: 0,
            lastAbsence: null,
            hasPreviousYearVacations: false,
            filteredVacations: []
          };
        }
        
        const days = parseInt(vac.days_count) || 1;
        employeeAbsences[empKey].totalDays += days;
        employeeAbsences[empKey].absenceCount++;

        // إضافة الإجازة للقائمة المفلترة
        employeeAbsences[empKey].filteredVacations.push(vac);

        // فحص إذا كانت الإجازة من العام المالي السابق
        if (isBeforeJune26(vac.vacation_date)) {
          employeeAbsences[empKey].hasPreviousYearVacations = true;
        }

        if (!employeeAbsences[empKey].lastAbsence ||
            vac.vacation_date > employeeAbsences[empKey].lastAbsence) {
          employeeAbsences[empKey].lastAbsence = vac.vacation_date;
        }
      });
      
      // فلترة الموظفين الذين لديهم غياب أكثر من الحد الأدنى
      let reportData = Object.values(employeeAbsences)
        .filter(emp => emp.totalDays >= minDays);

      // فلترة حسب الإدارة إذا تم تحديدها
      if (department) {
        reportData = reportData.filter(emp => emp.department === department);
      }

      // ترتيب النتائج
      reportData = reportData.sort((a, b) => b.totalDays - a.totalDays);
      
      displayAbsenceReport(reportData);
      hideLoading();
      
    } catch (error) {
      console.error('خطأ في إنشاء تقرير الغياب:', error);
      hideLoading();
      alert('حدث خطأ في إنشاء التقرير');
    }
  }
  
  // عرض تقرير الغياب
  function displayAbsenceReport(data) {
    const tbody = document.getElementById('absenceReportTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    if (!data || data.length === 0) {
      tbody.innerHTML = '<tr><td colspan="7" class="no-results">لا توجد بيانات غياب تطابق المعايير المحددة</td></tr>';
      return;
    }

    data.forEach(row => {
      const tr = document.createElement('tr');
      tr.innerHTML = `
        <td>${row.code || 'غير محدد'}</td>
        <td>${row.name || 'غير محدد'}</td>
        <td>${row.department || 'غير محدد'}</td>
        <td><strong>${row.totalDays || 0}</strong></td>
        <td>${row.absenceCount || 0}</td>
        <td>${formatDateSafe(row.lastAbsence)}</td>
        <td><button class="details-btn" data-employee-code="${row.code}" data-employee-name="${row.name}">عرض التفاصيل</button></td>
      `;

      // التحقق من وجود إجازات من العام المالي السابق
      if (row.hasPreviousYearVacations) {
        tr.style.backgroundColor = '#ffebee'; // خلفية حمراء فاتحة
        tr.style.color = '#c62828'; // نص أحمر داكن
        tr.title = 'يحتوي على إجازات من العام المالي السابق (قبل 26-6)';
      }

      // إضافة event listener لزر التفاصيل
      const detailsBtn = tr.querySelector('.details-btn');
      detailsBtn.addEventListener('click', async () => {
        await showEmployeeVacationDetails(row.code, row.name, row.filteredVacations);
      });

      tbody.appendChild(tr);
    });
  }
  
  // التقرير المخصص للموظف
  async function generateCustomEmployeeReport() {
    try {
      const searchValue = document.getElementById('customEmployeeSearch').value;
      const year = document.getElementById('customEmployeeYear').value;
      const startDate = document.getElementById('customEmployeeStartDate').value;
      const endDate = document.getElementById('customEmployeeEndDate').value;
      
      if (!searchValue) {
        alert('يرجى اختيار موظف');
        return;
      }
      
      showLoading('جاري إنشاء التقرير...');
      
      // التحقق من وجود البيانات
      if (!employees || employees.length === 0) {
        alert('لا توجد بيانات موظفين. يرجى التأكد من تحميل بيانات الموظفين أولاً.');
        hideLoading();
        return;
      }

      if (!vacations || vacations.length === 0) {
        alert('لا توجد بيانات إجازات. يرجى إضافة بعض الإجازات أولاً.');
        hideLoading();
        return;
      }
      
      // استخراج كود الموظف من النص المدخل
      const employeeCode = searchValue.split(' - ')[0];
      const employee = employees.find(emp => {
        return emp.code == employeeCode ||
               emp.full_name === searchValue ||
               emp.name === searchValue ||
               `${emp.code} - ${emp.full_name}` === searchValue;
      });
      
      if (!employee) {
        alert('الموظف غير موجود');
        hideLoading();
        return;
      }
      
      // فلترة إجازات الموظف
      let empVacations = vacations.filter(vac => {
        return vac.employee_code == employee.code ||
               vac.employee_name === employee.full_name ||
               vac.employee_name === employee.name;
      });
      
      if (startDate) {
        const filterStartDate = new Date(startDate);
        filterStartDate.setHours(0, 0, 0, 0);
        empVacations = empVacations.filter(vac => {
          const vacationDate = new Date(vac.vacation_date);
          vacationDate.setHours(0, 0, 0, 0);
          return vacationDate >= filterStartDate;
        });
      }
      
      if (endDate) {
        const filterEndDate = new Date(endDate);
        filterEndDate.setHours(23, 59, 59, 999);
        empVacations = empVacations.filter(vac => {
          const vacationDate = new Date(vac.vacation_date);
          vacationDate.setHours(0, 0, 0, 0);
          return vacationDate <= filterEndDate;
        });
      }
      
      if (year) {
        empVacations = empVacations.filter(vac => {
          const vacYear = new Date(vac.vacation_date).getFullYear();
          return vacYear == year;
        });
      }
      
      // حساب الإحصائيات باستخدام نفس منطق قاعدة البيانات
      const stats = await calculateCorrectEmployeeVacationStats(employee.code, empVacations);

      // عرض معلومات الموظف
      displayCustomEmployeeInfo(employee, stats);
      
      // عرض تفاصيل الإجازات
      displayCustomEmployeeVacations(empVacations);
      
      hideLoading();
      
    } catch (error) {
      console.error('خطأ في إنشاء التقرير المخصص:', error);
      hideLoading();
      alert('حدث خطأ في إنشاء التقرير');
    }
  }
  
  // حساب إحصائيات إجازات الموظف (الدالة القديمة للتوافق)
  function calculateEmployeeVacationStats(vacations) {
    const stats = {
      balance: 30, // رصيد افتراضي
      used: 0,
      remaining: 30
    };

    // حساب الإجازات المستخدمة باستخدام عدد الأيام الفعلي (باستثناء الإجازات خارج الرصيد وبدون راتب)
    vacations.forEach(vac => {
      const vacType = vac.vacation_type || vac.type;
      // استثناء الإجازات خارج الرصيد وإجازات بدون راتب
      if (vacType !== 'official' && vacType !== 'unpaid') {
        const days = parseInt(vac.days_count) || 1;
        stats.used += days;
      }
    });

    stats.remaining = Math.max(0, stats.balance - stats.used);

    return stats;
  }

  // حساب إحصائيات إجازات الموظف الصحيحة (تستخدم نفس منطق قاعدة البيانات)
  async function calculateCorrectEmployeeVacationStats(employeeCode, vacations) {
    try {
      // استخدام endpoint الخادم للحصول على الحساب الصحيح
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/api/employees/${employeeCode}/leave-balance`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const leaveData = await response.json();
        console.log('إحصائيات الموظف من الخادم:', leaveData);
        return {
          balance: leaveData.calculated_balance || 30,
          used: leaveData.used_days || 0,
          remaining: leaveData.remaining_days || 30
        };
      }

      // إذا فشل الاستعلام، استخدم الحساب المحلي
      console.log('فشل في جلب إحصائيات الموظف، استخدام الحساب المحلي');
      return calculateEmployeeVacationStatsLocal(vacations);

    } catch (error) {
      console.error('خطأ في حساب إحصائيات الموظف:', error);
      return calculateEmployeeVacationStatsLocal(vacations);
    }
  }

  // حساب محلي للإحصائيات (كبديل)
  function calculateEmployeeVacationStatsLocal(vacations) {
    // تحديد سنة الإجازة (من 26 يونيو إلى 25 يونيو)
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();

    let vacationYear = currentYear;
    if (currentDate.getMonth() < 5 || (currentDate.getMonth() === 5 && currentDate.getDate() < 26)) {
      vacationYear = currentYear - 1;
    }

    const vacationStartDate = new Date(vacationYear, 5, 26);
    const vacationEndDate = new Date(vacationYear + 1, 5, 25);

    const stats = {
      balance: 30,
      used: 0,
      remaining: 30
    };

    // حساب الإجازات المستخدمة في الفترة الصحيحة (باستثناء الإجازات خارج الرصيد)
    vacations.forEach(vac => {
      const vacationDate = new Date(vac.vacation_date);

      if (vacationDate >= vacationStartDate && vacationDate <= vacationEndDate) {
        const vacType = vac.vacation_type || vac.type;
        if (vacType !== 'official' && vacType !== 'unpaid') {
          const days = parseInt(vac.days_count) || 1;
          stats.used += days;
        }
      }
    });

    stats.remaining = Math.max(0, stats.balance - stats.used);
    return stats;
  }

  // دالة حساب رصيد الإجازات المحلي (من 26-6 إلى 25-6) - تستخدم نفس منطق قاعدة البيانات
  async function calculateLocalLeaveBalance(employeeCode) {
    try {
      // استخدام نفس endpoint الخادم للحصول على الحساب الصحيح
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/api/employees/${employeeCode}/leave-balance`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const leaveData = await response.json();
        console.log('بيانات الرصيد المحلي من الخادم:', leaveData);
        return {
          balance: leaveData.calculated_balance?.toString() || '30',
          used: leaveData.used_days?.toString() || '0',
          remaining: leaveData.remaining_days?.toString() || '30'
        };
      }

      // إذا فشل الاستعلام، استخدم الحساب المحلي كبديل
      console.log('فشل في جلب البيانات من الخادم، استخدام الحساب المحلي');
      return await calculateLocalLeaveBalanceFallback(employeeCode);

    } catch (error) {
      console.error('خطأ في حساب الرصيد المحلي:', error);
      return await calculateLocalLeaveBalanceFallback(employeeCode);
    }
  }

  // دالة احتياطية لحساب الرصيد محلياً
  async function calculateLocalLeaveBalanceFallback(employeeCode) {
    try {
      // تحديد سنة الإجازة (من 26 يونيو إلى 25 يونيو)
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();

      let vacationYear = currentYear;
      if (currentDate.getMonth() < 5 || (currentDate.getMonth() === 5 && currentDate.getDate() < 26)) {
        // إذا كنا قبل 26 يونيو، فنحن في سنة الإجازة السابقة
        vacationYear = currentYear - 1;
      }

      const vacationStartDate = new Date(vacationYear, 5, 26); // 26 يونيو
      const vacationEndDate = new Date(vacationYear + 1, 5, 25); // 25 يونيو السنة التالية

      // جلب إجازات الموظف في الفترة المحددة من البيانات المحلية
      const employeeVacations = getEmployeeVacationsFromLocal(employeeCode);

      if (!employeeVacations || employeeVacations.length === 0) {
        console.log('لا توجد إجازات للموظف في البيانات المحلية');
        return { balance: '30', used: '0', remaining: '30' };
      }

      // حساب الإجازات المستخدمة في الفترة (باستثناء الإجازات خارج الرصيد)
      let usedDays = 0;
      employeeVacations.forEach(vacation => {
        const vacationDate = new Date(vacation.vacation_date);

        // التحقق من أن الإجازة في الفترة المحددة
        if (vacationDate >= vacationStartDate && vacationDate <= vacationEndDate) {
          // حساب الإجازات المستخدمة (باستثناء الإجازات خارج الرصيد وإجازات بدون راتب)
          if (vacation.vacation_type !== 'official' && vacation.vacation_type !== 'unpaid') {
            usedDays += parseInt(vacation.days_count) || 1;
          }
        }
      });

      // الرصيد الافتراضي 30 يوم
      const balance = 30;
      const remaining = Math.max(0, balance - usedDays);

      return {
        balance: balance.toString(),
        used: usedDays.toString(),
        remaining: remaining.toString()
      };

    } catch (error) {
      console.error('خطأ في حساب الرصيد الاحتياطي:', error);
      return { balance: '30', used: '0', remaining: '30' };
    }
  }
  
  // عرض معلومات الموظف في التقرير المخصص
  function displayCustomEmployeeInfo(employee, stats) {
    const codeEl = document.getElementById('customEmployeeCode');
    const nameEl = document.getElementById('customEmployeeName');
    const deptEl = document.getElementById('customEmployeeDept');
    const balanceEl = document.getElementById('customEmployeeBalance');
    const usedEl = document.getElementById('customEmployeeUsed');
    const remainingEl = document.getElementById('customEmployeeRemaining');
    const infoEl = document.getElementById('customEmployeeInfo');
    
    if (codeEl) codeEl.textContent = employee.code || 'غير محدد';
    if (nameEl) nameEl.textContent = employee.full_name || employee.name || 'غير محدد';
    if (deptEl) deptEl.textContent = employee.department || 'غير محدد';
    if (balanceEl) balanceEl.textContent = stats.balance || 0;
    if (usedEl) usedEl.textContent = stats.used || 0;
    if (remainingEl) remainingEl.textContent = stats.remaining || 0;
    if (infoEl) infoEl.style.display = 'block';
  }
  
  // عرض إجازات الموظف في التقرير المخصص
  function displayCustomEmployeeVacations(vacations) {
    const tbody = document.getElementById('customEmployeeTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    if (!vacations || vacations.length === 0) {
      tbody.innerHTML = '<tr><td colspan="5" class="no-results">لا توجد إجازات للموظف في الفترة المحددة</td></tr>';
      // إخفاء الملحوظة عند عدم وجود إجازات
      const customEmployeePreviousYearNote = document.getElementById('customEmployeePreviousYearNote');
      if (customEmployeePreviousYearNote) {
        customEmployeePreviousYearNote.style.display = 'none';
      }
      return;
    }

    // ترتيب الإجازات حسب التاريخ (الأحدث أولاً)
    const sortedVacations = vacations.sort((a, b) => new Date(b.vacation_date) - new Date(a.vacation_date));

    // متغير لتتبع وجود إجازات من العام السابق
    let hasCustomEmployeePreviousYearVacations = false;
    
    sortedVacations.forEach(vac => {
      const tr = document.createElement('tr');

      // تحويل نوع الإجازة إلى النص العربي
      let vacationTypeText = '';
      const vacType = vac.vacation_type || vac.type;
      switch (vacType) {
        case 'casual':
          vacationTypeText = 'إجازة عارضة';
          break;
        case 'permission':
          vacationTypeText = 'غياب بإذن';
          break;
        case 'absence':
          vacationTypeText = 'غياب بدون إذن';
          break;
        case 'annual':
          vacationTypeText = 'إجازة سنوية';
          break;
        case 'unpaid':
          vacationTypeText = 'إجازة بدون راتب';
          break;
        case 'sick':
          vacationTypeText = 'إجازة مرضية';
          break;
        case 'official':
          vacationTypeText = 'إجازات خارج الرصيد';
          break;
        default:
          vacationTypeText = vacType || 'غير محدد';
      }

      // تحويل نوع الإجازة إلى النص العربي
      let officialTypeText = '-';
      if (vacType === 'official' && (vac.official_holiday_type || vac.official_type)) {
        const officialType = vac.official_holiday_type || vac.official_type;
        const officialTypeMap = {
          // الإجازات خارج الرصيد الموجودة
          'police_day': 'عيد الشرطة (25 يناير)',
          'sinai_day': 'عيد تحرير سيناء',
          'labor_day': 'عيد العمال',
          'june_revolution': 'عيد ثورة 30 يونيو',
          'july_revolution': 'عيد ثورة 23 يوليو',
          'armed_forces_day': 'عيد القوات المسلحة (6 أكتوبر)',
          'christmas': 'عيد الميلاد المجيد',
          'sham_el_nessim': 'عيد شم النسيم',
          'islamic_new_year': 'رأس السنة الهجرية',
          'prophet_birthday': 'المولد النبوي الشريف',
          'eid_adha': 'عيد الأضحى',
          'eid_fitr': 'عيد الفطر',
          // الأنواع الجديدة المضافة بعد عيد الفطر
          'emergency': 'اجازة اضطرارية',
          'birth': 'مولود',
          'maternity': 'اجازة وضع',
          'marriage': 'زواج',
          'death_first_degree': 'الوفاة من الدرجة الاولى',
          'military_service': 'الاستدعاء للجيش',
          'exams': 'الامتحانات',
          'pilgrimage': 'الحج والعمرة',
          // الأنواع القديمة للتوافق مع البيانات الموجودة
          'new_year': 'رأس السنة الميلادية',
          'coptic_christmas': 'عيد الميلاد المجيد',
          'january_revolution': 'ثورة 25 يناير',
          'sinai_liberation': 'تحرير سيناء',
          'arafat_day': 'يوم عرفة',
          'eid_al_adha': 'عيد الأضحى المبارك',
          'eid_al_adha_2': 'عيد الأضحى المبارك (اليوم الثاني)',
          'eid_al_adha_3': 'عيد الأضحى المبارك (اليوم الثالث)',
          'eid_al_fitr': 'عيد الفطر المبارك',
          'eid_al_fitr_2': 'عيد الفطر المبارك (اليوم الثاني)',
          'eid_al_fitr_3': 'عيد الفطر المبارك (اليوم الثالث)',
          'other': 'أخرى'
        };
        officialTypeText = officialTypeMap[officialType] || officialType;
      }
      
      // تحديد نص التاريخ بناءً على وجود start_date و end_date
      let dateText = '';
      if (vac.start_date && vac.end_date) {
        if (vac.start_date === vac.end_date) {
          // يوم واحد
          dateText = formatDateSafe(vac.start_date);
        } else {
          // فترة
          dateText = `من ${formatDateSafe(vac.start_date)} إلى ${formatDateSafe(vac.end_date)}`;
        }
      } else {
        // للتوافق مع البيانات القديمة
        dateText = formatDateSafe(vac.vacation_date);
      }

      tr.innerHTML = `
        <td>${vacationTypeText}</td>
        <td>${dateText}</td>
        <td><strong>${vac.days_count || 1}</strong></td>
        <td>${officialTypeText}</td>
        <td>-</td>
      `;

      // إضافة اللون الأحمر للإجازات قبل 26-6 (العام المالي السابق)
      const vacationDate = vac.start_date || vac.vacation_date;
      if (isBeforeJune26(vacationDate)) {
        tr.style.backgroundColor = '#ffebee'; // خلفية حمراء فاتحة
        tr.style.color = '#c62828'; // نص أحمر داكن
        tr.style.fontWeight = 'bold';
        tr.title = 'إجازة من العام المالي السابق (قبل 26-6)';
        hasCustomEmployeePreviousYearVacations = true; // تحديث المتغير
      }

      tbody.appendChild(tr);
    });

    // إظهار/إخفاء ملحوظة الإجازات من العام السابق
    const customEmployeePreviousYearNote = document.getElementById('customEmployeePreviousYearNote');
    if (customEmployeePreviousYearNote) {
      if (hasCustomEmployeePreviousYearVacations) {
        customEmployeePreviousYearNote.style.display = 'block';
      } else {
        customEmployeePreviousYearNote.style.display = 'none';
      }
    }
  }
  
  // تصدير التقارير إلى Excel
  function exportEmployeeSummaryReport() {
    exportTableToExcel('employeeSummaryTable', 'تقرير_إجمالي_الإجازات_حسب_الموظف');
  }
  
  function exportDepartmentSummaryReport() {
    exportTableToExcel('deptSummaryTable', 'تقرير_الإجازات_حسب_الإدارة');
  }
  
  function exportAbsenceReport() {
    exportTableToExcel('absenceReportTable', 'تقرير_الغياب_المتكرر');
  }
  
  function exportCustomEmployeeReport() {
    exportTableToExcel('customEmployeeTable', 'التقرير_المخصص_للموظف');
  }
  
  // طباعة التقرير المخصص
  function printCustomEmployeeReport() {
    const employeeInfo = document.getElementById('customEmployeeInfo');
    const tableContainer = document.querySelector('#custom-employee .report-table-container');
    
    if (!employeeInfo || !tableContainer) {
      alert('لا توجد بيانات للطباعة');
      return;
    }
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html dir="rtl">
        <head>
          <title>التقرير المخصص للموظف</title>
          <style>
            body { font-family: Arial, sans-serif; direction: rtl; text-align: right; }
            .employee-info-card { background-color: #f8f9fa; padding: 20px; margin-bottom: 20px; border-radius: 8px; }
            .info-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; }
            .info-item { display: flex; justify-content: space-between; padding: 10px; background: white; border-radius: 4px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
            th { background-color: #343a40; color: white; }
            @media print { body { margin: 0; } }
          </style>
        </head>
        <body>
          <h1 style="text-align: center;">التقرير المخصص للموظف</h1>
          ${employeeInfo.outerHTML}
          ${tableContainer.outerHTML}
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  }
  
  // وظائف مساعدة
  function showLoading(message = 'جاري التحميل...') {
    console.log(message);
  }

  function hideLoading() {
    console.log('تم الانتهاء من التحميل');
  }

  // معالجة خطأ التوكن منتهي الصلاحية
  function handleTokenExpired() {
    alert('انتهت صلاحية جلسة العمل. سيتم إعادة توجيهك لصفحة تسجيل الدخول.');
    localStorage.removeItem('token');
    localStorage.removeItem('permissions');
    window.location.href = 'login.html';
  }

  // التحقق من وجود إجازة مكررة لنفس الموظف في نفس اليوم
  function checkDuplicateVacation(employeeCode, vacationDate) {
    // التحقق في الإجازات المضافة محلياً
    const localDuplicate = addedVacations.find(v =>
      v.employee_code === employeeCode &&
      v.vacation_date === vacationDate
    );

    if (localDuplicate) {
      return {
        isDuplicate: true,
        source: 'local',
        vacation: localDuplicate
      };
    }

    // التحقق في الإجازات المحملة من قاعدة البيانات
    const dbDuplicate = vacations.find(v =>
      v.employee_code === employeeCode &&
      v.vacation_date === vacationDate
    );

    if (dbDuplicate) {
      return {
        isDuplicate: true,
        source: 'database',
        vacation: dbDuplicate
      };
    }

    return { isDuplicate: false };
  }
  

  
  function exportTableToExcel(tableId, filename) {
    const table = document.getElementById(tableId);
    if (!table) {
      alert('الجدول غير موجود');
      return;
    }
    
    // تحويل الجدول إلى CSV
    let csv = [];
    const rows = table.querySelectorAll('tr');
    
    for (let i = 0; i < rows.length; i++) {
      const row = [];
      const cols = rows[i].querySelectorAll('td, th');
      
      for (let j = 0; j < cols.length; j++) {
        row.push('"' + cols[j].innerText.replace(/"/g, '""') + '"');
      }
      
      csv.push(row.join(','));
    }
    
    // تحميل الملف
    const csvContent = csv.join('\n');
    const blob = new Blob(["\uFEFF" + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename + '.csv');
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }

  // ===== التقارير الجديدة =====

  // تقرير الأيام الأعلى غيابًا
  async function generateHighestAbsenceDaysReport() {
    try {
      showLoading();

      const startDate = document.getElementById('absenceDaysStartDate').value;
      const endDate = document.getElementById('absenceDaysEndDate').value;
      const analysisType = document.getElementById('absenceDaysType').value;

      // التحقق من وجود البيانات
      if (!employees || employees.length === 0) {
        alert('لا توجد بيانات موظفين. يرجى التأكد من تحميل بيانات الموظفين أولاً.');
        hideLoading();
        return;
      }

      if (!vacations || vacations.length === 0) {
        alert('لا توجد بيانات إجازات. يرجى إضافة بعض الإجازات أولاً.');
        hideLoading();
        return;
      }

      // فلترة إجازات الغياب بدون إذن
      let absenceVacations = vacations.filter(vac => {
        const vacType = vac.vacation_type || vac.type;
        return vacType === 'absence' || vacType === 'غياب بدون إذن' || vacType === 'إجازة بدون إذن';
      });

      // تطبيق فلتر التاريخ
      if (startDate && endDate) {
        const filterStartDate = new Date(startDate);
        filterStartDate.setHours(0, 0, 0, 0);
        const filterEndDate = new Date(endDate);
        filterEndDate.setHours(23, 59, 59, 999);
        absenceVacations = absenceVacations.filter(vac => {
          const vacDate = new Date(vac.vacation_date);
          vacDate.setHours(0, 0, 0, 0);
          return vacDate >= filterStartDate && vacDate <= filterEndDate;
        });
      }

      let reportData = [];

      if (analysisType === 'specific_dates') {
        // تحليل التواريخ المحددة
        const dateGroups = {};

        absenceVacations.forEach(vac => {
          const dateKey = vac.vacation_date;
          if (!dateGroups[dateKey]) {
            dateGroups[dateKey] = {
              date: dateKey,
              absenceCount: 0,
              employeeCount: new Set()
            };
          }
          dateGroups[dateKey].absenceCount++;
          dateGroups[dateKey].employeeCount.add(vac.employee_code);
        });

        reportData = Object.values(dateGroups).map(group => ({
          dateOrDay: formatDateSafe(group.date),
          absenceCount: group.absenceCount,
          employeeCount: group.employeeCount.size,
          percentage: ((group.employeeCount.size / employees.length) * 100).toFixed(2) + '%'
        }));

        // ترتيب حسب عدد الموظفين الغائبين
        reportData.sort((a, b) => b.employeeCount - a.employeeCount);

      } else if (analysisType === 'weekdays') {
        // تحليل أيام الأسبوع
        const weekdayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        const weekdayGroups = {};

        absenceVacations.forEach(vac => {
          const date = new Date(vac.vacation_date);
          const weekday = date.getDay();
          const weekdayName = weekdayNames[weekday];

          if (!weekdayGroups[weekdayName]) {
            weekdayGroups[weekdayName] = {
              absenceCount: 0,
              employeeCount: new Set()
            };
          }
          weekdayGroups[weekdayName].absenceCount++;
          weekdayGroups[weekdayName].employeeCount.add(vac.employee_code);
        });

        reportData = weekdayNames.map(dayName => {
          const group = weekdayGroups[dayName] || { absenceCount: 0, employeeCount: new Set() };
          return {
            dateOrDay: dayName,
            absenceCount: group.absenceCount,
            employeeCount: group.employeeCount.size,
            percentage: ((group.employeeCount.size / employees.length) * 100).toFixed(2) + '%'
          };
        });

        // ترتيب حسب عدد الموظفين الغائبين
        reportData.sort((a, b) => b.employeeCount - a.employeeCount);
      }

      displayHighestAbsenceDaysReport(reportData);
      hideLoading();

    } catch (error) {
      console.error('خطأ في إنشاء تقرير الأيام الأعلى غيابًا:', error);
      hideLoading();
      alert('حدث خطأ في إنشاء التقرير');
    }
  }

  // عرض تقرير الأيام الأعلى غيابًا
  function displayHighestAbsenceDaysReport(data) {
    const tbody = document.getElementById('absenceDaysReportTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    if (!data || data.length === 0) {
      tbody.innerHTML = '<tr><td colspan="4" class="no-results">لا توجد بيانات غياب في الفترة المحددة</td></tr>';
      return;
    }

    data.forEach(row => {
      const tr = document.createElement('tr');
      tr.innerHTML = `
        <td>${row.dateOrDay}</td>
        <td>${row.absenceCount}</td>
        <td>${row.employeeCount}</td>
        <td>${row.percentage}</td>
      `;
      tbody.appendChild(tr);
    });
  }

  // تصدير تقرير الأيام الأعلى غيابًا
  function exportHighestAbsenceDaysReport() {
    exportTableToExcel('absenceDaysReportTable', 'تقرير_الأيام_الأعلى_غيابًا');
  }

  // تقرير الموظفين الأعلى استخدامًا للإجازات
  async function generateTopVacationUsersReport() {
    try {
      showLoading();

      const startDate = document.getElementById('topUsersStartDate').value;
      const endDate = document.getElementById('topUsersEndDate').value;
      const department = document.getElementById('topUsersDepartment').value;
      const limit = document.getElementById('topUsersLimit').value;

      // التحقق من وجود البيانات
      if (!employees || employees.length === 0) {
        alert('لا توجد بيانات موظفين. يرجى التأكد من تحميل بيانات الموظفين أولاً.');
        hideLoading();
        return;
      }

      if (!vacations || vacations.length === 0) {
        alert('لا توجد بيانات إجازات. يرجى إضافة بعض الإجازات أولاً.');
        hideLoading();
        return;
      }

      // فلترة الإجازات حسب التاريخ
      let filteredVacations = [...vacations];
      if (startDate && endDate) {
        const filterStartDate = new Date(startDate);
        filterStartDate.setHours(0, 0, 0, 0);
        const filterEndDate = new Date(endDate);
        filterEndDate.setHours(23, 59, 59, 999);
        filteredVacations = filteredVacations.filter(vac => {
          const vacDate = new Date(vac.vacation_date);
          vacDate.setHours(0, 0, 0, 0);
          return vacDate >= filterStartDate && vacDate <= filterEndDate;
        });
      }

      // تجميع البيانات حسب الموظف
      const employeeStats = {};

      employees.forEach(employee => {
        // فلترة حسب الإدارة إذا تم تحديدها
        if (department && employee.department !== department) {
          return;
        }

        const empVacations = filteredVacations.filter(vac => {
          return vac.employee_code == employee.code ||
                 vac.employee_name === employee.full_name ||
                 vac.employee_name === employee.name;
        });

        if (empVacations.length > 0) {
          const stats = {
            code: employee.code,
            name: employee.full_name || employee.name,
            department: employee.department,
            totalDays: 0,
            vacationCount: empVacations.length,
            vacationTypes: {},
            hasPreviousYearVacations: false,
            filteredVacations: empVacations
          };

          empVacations.forEach(vac => {
            // فحص إذا كانت الإجازة من العام المالي السابق
            if (isBeforeJune26(vac.vacation_date)) {
              stats.hasPreviousYearVacations = true;
            }

            // استخدام عدد الأيام الفعلي من قاعدة البيانات
            const days = parseInt(vac.days_count) || 1;
            stats.totalDays += days;

            const vacType = vac.vacation_type || vac.type;
            if (!stats.vacationTypes[vacType]) {
              stats.vacationTypes[vacType] = 0;
            }
            stats.vacationTypes[vacType] += days;
          });

          // حساب متوسط أيام الإجازة
          stats.avgDays = (stats.totalDays / stats.vacationCount).toFixed(1);

          // تحديد النوع الأكثر استخداماً
          let maxType = '';
          let maxDays = 0;
          for (const [type, days] of Object.entries(stats.vacationTypes)) {
            if (days > maxDays) {
              maxDays = days;
              maxType = type;
            }
          }

          // تحويل نوع الإجازة إلى العربية
          const typeMap = {
            'casual': 'عارضة',
            'annual': 'سنوية',
            'permission': 'غياب بإذن',
            'absence': 'غياب بدون إذن',
            'sick': 'مرضية',
            'unpaid': 'بدون راتب',
            'official': 'إجازات خارج الرصيد'
          };
          stats.mostUsedType = typeMap[maxType] || maxType || 'غير محدد';

          employeeStats[employee.code] = stats;
        }
      });

      // تحويل إلى مصفوفة وترتيب حسب إجمالي الأيام
      let reportData = Object.values(employeeStats).sort((a, b) => b.totalDays - a.totalDays);

      // تطبيق الحد الأقصى للعدد
      if (limit !== 'all') {
        reportData = reportData.slice(0, parseInt(limit));
      }

      // إضافة الترتيب
      reportData = reportData.map((emp, index) => ({
        ...emp,
        rank: index + 1
      }));

      displayTopVacationUsersReport(reportData);
      hideLoading();

    } catch (error) {
      console.error('خطأ في إنشاء تقرير الموظفين الأعلى استخدامًا للإجازات:', error);
      hideLoading();
      alert('حدث خطأ في إنشاء التقرير');
    }
  }

  // عرض تقرير الموظفين الأعلى استخدامًا للإجازات
  function displayTopVacationUsersReport(data) {
    const tbody = document.getElementById('topUsersReportTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    if (!data || data.length === 0) {
      tbody.innerHTML = '<tr><td colspan="9" class="no-results">لا توجد بيانات إجازات في الفترة المحددة</td></tr>';
      return;
    }

    data.forEach(row => {
      const tr = document.createElement('tr');
      tr.innerHTML = `
        <td>${row.rank}</td>
        <td>${row.code || 'غير محدد'}</td>
        <td>${row.name || 'غير محدد'}</td>
        <td>${row.department || 'غير محدد'}</td>
        <td><strong>${row.totalDays}</strong></td>
        <td>${row.vacationCount}</td>
        <td>${row.avgDays}</td>
        <td>${row.mostUsedType}</td>
        <td><button class="details-btn" data-employee-code="${row.code}" data-employee-name="${row.name}">عرض التفاصيل</button></td>
      `;

      // التحقق من وجود إجازات من العام المالي السابق
      if (row.hasPreviousYearVacations) {
        tr.style.backgroundColor = '#ffebee'; // خلفية حمراء فاتحة
        tr.style.color = '#c62828'; // نص أحمر داكن
        tr.title = 'يحتوي على إجازات من العام المالي السابق (قبل 26-6)';
      }

      // إضافة event listener لزر التفاصيل
      const detailsBtn = tr.querySelector('.details-btn');
      detailsBtn.addEventListener('click', async () => {
        await showEmployeeVacationDetails(row.code, row.name, row.filteredVacations);
      });

      tbody.appendChild(tr);
    });
  }

  // تصدير تقرير الموظفين الأعلى استخدامًا للإجازات
  function exportTopVacationUsersReport() {
    exportTableToExcel('topUsersReportTable', 'تقرير_الموظفين_الأعلى_استخدامًا_للإجازات');
  }

  // دالة عرض تفاصيل إجازات الموظف (تستخدم من التقارير)
  async function showEmployeeVacationDetails(employeeCode, employeeName, filteredVacations = null) {
    console.log('محاولة عرض تفاصيل الموظف:', employeeCode, employeeName);
    console.log('عدد الإجازات المحلية المتاحة:', vacations ? vacations.length : 'غير محدد');
    console.log('عدد الموظفين المتاحين:', employees ? employees.length : 'غير محدد');

    // التأكد من تحميل البيانات
    if (!employees || employees.length === 0) {
      console.log('البيانات غير محملة، محاولة تحميلها...');
      try {
        await loadEmployees();
      } catch (error) {
        console.error('فشل في تحميل الموظفين:', error);
      }
    }

    if (!vacations || vacations.length === 0) {
      console.log('الإجازات غير محملة، محاولة تحميلها...');
      try {
        await loadVacations();
      } catch (error) {
        console.error('فشل في تحميل الإجازات:', error);
      }
    }

    // البحث عن الموظف في قائمة الموظفين
    const employee = employees.find(emp =>
      emp.code == employeeCode ||
      emp.full_name === employeeName ||
      emp.name === employeeName
    );

    if (!employee) {
      console.error('لم يتم العثور على الموظف في القائمة:', employeeCode, employeeName);
      console.log('قائمة الموظفين المتاحة:', employees.map(e => ({code: e.code, name: e.full_name || e.name})));
      alert('لم يتم العثور على بيانات الموظف');
      return;
    }

    console.log('تم العثور على الموظف:', employee);

    // إنشاء كائن موظف للعرض
    const employeeVacation = {
      code: employee.code,
      name: employee.full_name || employee.name,
      department: employee.department
    };

    // إذا تم تمرير إجازات مفلترة، استخدمها
    if (filteredVacations && filteredVacations.length > 0) {
      // تحويل الإجازات المفلترة إلى التنسيق المطلوب وإضافتها لكائن الموظف
      employeeVacation.vacationDetails = filteredVacations.map(vac => ({
        id: vac.id,
        type: vac.vacation_type || vac.type,
        date: vac.vacation_date,
        days: vac.days_count || 1,
        officialType: vac.official_type
      }));
      console.log('استخدام الإجازات المفلترة من التقرير:', employeeVacation.vacationDetails.length);
    }

    // استخدام دالة عرض التفاصيل الموجودة
    showVacationDetails(employeeVacation);
  }

  // دالة للحصول على إجازات الموظف من البيانات المحلية
  function getEmployeeVacationsFromLocal(employeeCode) {
    console.log('البحث عن إجازات الموظف محلياً:', employeeCode);
    console.log('إجمالي الإجازات المحلية:', vacations ? vacations.length : 'غير محدد');

    if (!vacations || vacations.length === 0) {
      console.log('لا توجد إجازات محلية');
      return [];
    }

    // طباعة عينة من الإجازات لفهم البنية
    if (vacations.length > 0) {
      console.log('عينة من الإجازات:', vacations.slice(0, 3));
    }

    // البحث عن إجازات الموظف في البيانات المحلية
    const employeeVacations = vacations.filter(vacation => {
      // تحويل كلاهما إلى string للمقارنة
      const vacationCode = String(vacation.employee_code || vacation.code || vacation.employeeCode || '');
      const searchCode = String(employeeCode || '');

      const match = vacationCode === searchCode ||
                   vacationCode == searchCode ||
                   vacation.employee_code == employeeCode ||
                   vacation.code == employeeCode ||
                   vacation.employeeCode == employeeCode;

      if (match) {
        console.log('تم العثور على إجازة متطابقة:', {
          vacation: vacation,
          vacationCode: vacationCode,
          searchCode: searchCode
        });
      }
      return match;
    });

    console.log('إجازات الموظف المحلية النهائية:', employeeVacations);
    return employeeVacations;
  }

  // إضافة الدالة إلى النطاق العام للوصول إليها من HTML
  window.showEmployeeVacationDetails = showEmployeeVacationDetails;

  // بدء التطبيق
  init();

  // تهيئة التقارير بعد تحميل البيانات
  setTimeout(() => {
    initializeReports();
  }, 1000);

  // إعداد فلاتر الإجازات المضافة
  setupVacationFilters();
});

// إعداد فلاتر الإجازات المضافة
function setupVacationFilters() {
  const applyVacationFiltersBtn = document.getElementById('applyVacationFiltersBtn');
  const clearVacationFiltersBtn = document.getElementById('clearVacationFiltersBtn');

  if (applyVacationFiltersBtn) {
    applyVacationFiltersBtn.addEventListener('click', function() {
      applyVacationFilters();
    });
  }

  if (clearVacationFiltersBtn) {
    clearVacationFiltersBtn.addEventListener('click', function() {
      clearVacationFilters();
    });
  }

  // إضافة البحث الفوري أثناء الكتابة
  const filterInputs = ['filterVacationId', 'filterVacationEmployeeCode', 'filterVacationEmployeeName', 'filterVacationFromDate', 'filterVacationToDate'];
  filterInputs.forEach(inputId => {
    const input = document.getElementById(inputId);
    if (input) {
      input.addEventListener('input', function() {
        clearTimeout(this.filterTimeout);
        this.filterTimeout = setTimeout(() => {
          applyVacationFilters();
        }, 300);
      });
    }
  });
}

// دالة مساعدة لتحويل التاريخ بصيغة DD/MM/YYYY إلى تاريخ قابل للمقارنة
function parseDateFromTable(dateText) {
  if (!dateText) return null;

  // محاولة تحليل التاريخ بصيغة DD/MM/YYYY مثل "01/07/2025"
  const ddmmyyyyPattern = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
  const match = dateText.match(ddmmyyyyPattern);

  if (match) {
    const day = match[1].padStart(2, '0');
    const month = match[2].padStart(2, '0');
    const year = match[3];

    return new Date(`${year}-${month}-${day}`);
  }

  // محاولة تحليل التاريخ بصيغة أخرى
  try {
    return new Date(dateText);
  } catch (e) {
    return null;
  }
}

// تطبيق فلاتر الإجازات المضافة
function applyVacationFilters() {
  const vacationId = document.getElementById('filterVacationId').value.trim();
  const employeeCode = document.getElementById('filterVacationEmployeeCode').value.trim();
  const employeeName = document.getElementById('filterVacationEmployeeName').value.trim();
  const fromDate = document.getElementById('filterVacationFromDate').value;
  const toDate = document.getElementById('filterVacationToDate').value;

  const tableBody = document.getElementById('addedVacationsTableBody');
  const rows = tableBody.querySelectorAll('tr');

  rows.forEach(row => {
    const cells = row.querySelectorAll('td');
    if (cells.length === 0) return; // تجاهل الصفوف الفارغة

    // استخراج البيانات من الخلايا
    const rowVacationId = cells[0]?.textContent?.trim() || ''; // كود الإجازة في العمود الأول
    const rowEmployeeCode = cells[1]?.textContent?.trim() || ''; // كود الموظف في العمود الثاني
    const rowEmployeeName = cells[2]?.textContent?.trim() || ''; // اسم الموظف في العمود الثالث
    const rowDate = cells[5]?.textContent?.trim() || ''; // تاريخ الإجازة في العمود السادس

    let showRow = true;

    // فلترة بكود الإجازة
    if (vacationId && !rowVacationId.includes(vacationId)) {
      showRow = false;
    }

    // فلترة بكود الموظف
    if (employeeCode && !rowEmployeeCode.includes(employeeCode)) {
      showRow = false;
    }

    // فلترة بالاسم
    if (employeeName && !rowEmployeeName.toLowerCase().includes(employeeName.toLowerCase())) {
      showRow = false;
    }

    // فلترة بالتاريخ من
    if (fromDate && rowDate) {
      const rowDateObj = parseDateFromTable(rowDate);
      const fromDateObj = new Date(fromDate);

      if (rowDateObj && fromDateObj && rowDateObj < fromDateObj) {
        showRow = false;
      }
    }

    // فلترة بالتاريخ إلى
    if (toDate && rowDate) {
      const rowDateObj = parseDateFromTable(rowDate);
      const toDateObj = new Date(toDate);

      if (rowDateObj && toDateObj && rowDateObj > toDateObj) {
        showRow = false;
      }
    }

    // إظهار أو إخفاء الصف
    row.style.display = showRow ? '' : 'none';
  });
}

// مسح فلاتر الإجازات المضافة
function clearVacationFilters() {
  document.getElementById('filterVacationId').value = '';
  document.getElementById('filterVacationEmployeeCode').value = '';
  document.getElementById('filterVacationEmployeeName').value = '';
  document.getElementById('filterVacationFromDate').value = '';
  document.getElementById('filterVacationToDate').value = '';

  // إظهار جميع الصفوف
  const tableBody = document.getElementById('addedVacationsTableBody');
  const rows = tableBody.querySelectorAll('tr');
  rows.forEach(row => {
    row.style.display = '';
  });
}

// دوال عامة للتحكم في الصفحات (يمكن استدعاؤها من HTML)
window.changePageAddedVacations = function(direction) {
  if (typeof changePageAddedVacations === 'function') {
    changePageAddedVacations(direction);
  }
};

window.goToPageAddedVacations = function(page) {
  if (typeof goToPageAddedVacations === 'function') {
    goToPageAddedVacations(page);
  }
};

window.changePageVacations = function(direction) {
  if (typeof changePageVacations === 'function') {
    changePageVacations(direction);
  }
};

window.goToPageVacations = function(page) {
  if (typeof goToPageVacations === 'function') {
    goToPageVacations(page);
  }
};