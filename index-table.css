/* جدول عصري أزرق ثابت مع فواصل واضحة لقاعدة بيانات الموظفين */

.employee-table-container {
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 4px 24px var(--shadow-color);
  padding: 24px 16px;
  margin-top: 24px;
  overflow-x: auto;
}

/* تم نقل تنسيقات الجداول إلى shared-styles.css */

/* تم نقل تنسيقات الأزرار إلى shared-styles.css */

/* تم نقل تنسيقات زر العرض إلى shared-styles.css */

@media (max-width: 700px) {
  .employee-table thead th, 
  .employee-table tbody td {
    font-size: 0.95rem;
    padding: 8px 4px;
  }
  
  .employee-table-container {
    padding: 8px 2px;
  }
}

/* تم نقل تنسيقات .table-controls إلى shared-styles.css */

.export-btn {
  background: var(--primary-color);
  color: #ffffff;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: background 0.2s;
}

.export-btn:hover {
  background: var(--primary-dark);
}

.delete-all {
  background-color: var(--danger-color);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.delete-all:hover {
  background-color: var(--danger-dark);
}

.calculate-leave {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
  margin-right: 10px;
}

.calculate-leave:hover {
  background-color: #218838;
}

.actions-bar {
  display: flex;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: #ffffff;
  margin: 5% auto;
  padding: 0;
  border-radius: 12px;
  width: 80%;
  max-width: 600px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  color: var(--primary-color);
  font-size: 1.5rem;
}

.close {
  color: var(--text-color);
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close:hover {
  color: var(--danger-color);
}

.modal-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Field Selection Styles */
.field-selection {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.select-all {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.fields-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.field-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
}

.field-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.field-checkbox label {
  cursor: pointer;
  font-size: 1rem;
}

/* Button Styles */
.confirm-btn, .cancel-btn {
  padding: 8px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.2s;
}

.confirm-btn {
  background: var(--primary-color);
  color: #ffffff;
}

.confirm-btn:hover {
  background: var(--primary-dark);
}

.cancel-btn {
  background: #f0f0f0;
  color: #333;
}

.cancel-btn:hover {
  background: #e0e0e0;
}

@media (max-width: 700px) {
  .modal-content {
    width: 95%;
    margin: 10% auto;
  }
  
  .fields-grid {
    grid-template-columns: 1fr;
  }
}

/* ===== تنسيقات نظام الصفحات ===== */
.pagination-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-top: 25px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  flex-wrap: wrap;
}

.pagination-btn {
  padding: 10px 15px;
  border: 2px solid var(--primary-color);
  background: var(--white);
  color: var(--primary-color);
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  min-width: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.pagination-btn:hover:not(:disabled) {
  background: var(--primary-color);
  color: #ffffff !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.pagination-btn.active {
  background: var(--primary-color);
  color: #ffffff !important;
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.page-btn {
  min-width: 40px;
  padding: 8px 12px;
}

.pagination-dots {
  color: var(--text-secondary);
  font-weight: bold;
  padding: 0 5px;
  font-size: 16px;
}

.page-info {
  background: var(--white);
  padding: 8px 15px;
  border-radius: 6px;
  color: var(--text-secondary);
  font-size: 13px;
  font-weight: 500;
  border: 1px solid var(--border-color);
  white-space: nowrap;
}

.prev-btn, .next-btn {
  font-weight: 700;
  padding: 10px 18px;
  background: linear-gradient(135deg, var(--primary-color) 0%, #1976d2 100%);
  color: #ffffff !important;
  border: none;
}

.prev-btn:hover:not(:disabled),
.next-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  color: #ffffff !important;
  transform: translateY(-2px);
}

/* التصميم المتجاوب للصفحات */
@media (max-width: 768px) {
  .pagination-container {
    gap: 10px;
    padding: 15px;
    flex-direction: column;
  }

  .page-numbers {
    order: 1;
    justify-content: center;
  }

  .prev-btn, .next-btn {
    order: 2;
    min-width: 100px;
  }

  .page-info {
    order: 3;
    text-align: center;
    width: 100%;
  }

  .pagination-btn {
    padding: 8px 12px;
    font-size: 13px;
  }

  .page-btn {
    min-width: 35px;
    padding: 6px 10px;
  }
}

@media (max-width: 480px) {
  .pagination-container {
    gap: 8px;
    padding: 12px;
  }

  .page-numbers {
    gap: 5px;
  }

  .pagination-btn {
    padding: 6px 10px;
    font-size: 12px;
    min-width: 35px;
  }

  .page-btn {
    min-width: 30px;
    padding: 5px 8px;
  }

  .prev-btn, .next-btn {
    padding: 8px 12px;
    min-width: 80px;
  }

  .page-info {
    font-size: 12px;
    padding: 6px 12px;
  }
}

/* تحسينات إضافية للمظهر */
.pagination-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0.3;
}

.pagination-container {
  position: relative;
}

/* تأثيرات التحويم المحسنة */
.pagination-btn:not(:disabled):not(.active):hover {
  background: linear-gradient(135deg, var(--primary-color) 0%, #1976d2 100%);
  color: #ffffff !important;
  border-color: #1976d2;
}

.page-btn.active {
  background: linear-gradient(135deg, var(--primary-color) 0%, #1976d2 100%);
  color: #ffffff !important;
  border-color: #1976d2;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);
}
