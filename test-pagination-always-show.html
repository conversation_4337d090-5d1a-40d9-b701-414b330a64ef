<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>اختبار إظهار أزرار الصفحات دائماً</title>
  <link rel="stylesheet" href="shared-styles.css">
  <link rel="stylesheet" href="pagination-styles.css">
  <style>
    body {
      padding: 20px;
      background: #f5f5f5;
    }
    .test-container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      margin-bottom: 30px;
    }
    .test-info {
      background: #e8f5e8;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
      border-left: 4px solid #4CAF50;
    }
    .test-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    .test-table th,
    .test-table td {
      border: 1px solid #ddd;
      padding: 12px;
      text-align: center;
    }
    .test-table th {
      background: #f5f5f5;
      font-weight: bold;
    }
    .test-table tr:nth-child(even) {
      background: #f9f9f9;
    }
    .example-section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 8px;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1>اختبار إظهار أزرار الصفحات دائماً</h1>
    
    <div class="test-info">
      <h3>✅ تم التحديث بنجاح!</h3>
      <p>الآن ستظهر أزرار السابق والتالي وأرقام الصفحات حتى لو كانت البيانات أقل من 50 صف.</p>
      <p><strong>التغيير:</strong> تم تعديل الشرط من <code>totalPages <= 1</code> إلى <code>totalItems === 0</code></p>
    </div>

    <!-- مثال مع 10 عناصر فقط -->
    <div class="example-section">
      <h3>مثال: جدول مع 10 عناصر فقط</h3>
      <table class="test-table">
        <thead>
          <tr>
            <th>الرقم</th>
            <th>الاسم</th>
            <th>الإدارة</th>
            <th>الوظيفة</th>
          </tr>
        </thead>
        <tbody>
          <tr><td>1</td><td>أحمد محمد</td><td>الموارد البشرية</td><td>مدير</td></tr>
          <tr><td>2</td><td>فاطمة علي</td><td>المحاسبة</td><td>محاسب</td></tr>
          <tr><td>3</td><td>محمد سالم</td><td>التسويق</td><td>مسوق</td></tr>
          <tr><td>4</td><td>نور أحمد</td><td>تقنية المعلومات</td><td>مطور</td></tr>
          <tr><td>5</td><td>سارة محمود</td><td>المبيعات</td><td>مندوب مبيعات</td></tr>
          <tr><td>6</td><td>خالد عبدالله</td><td>الموارد البشرية</td><td>موظف</td></tr>
          <tr><td>7</td><td>ليلى حسن</td><td>المحاسبة</td><td>محاسب مساعد</td></tr>
          <tr><td>8</td><td>عمر يوسف</td><td>التسويق</td><td>مصمم</td></tr>
          <tr><td>9</td><td>مريم سعد</td><td>تقنية المعلومات</td><td>محلل نظم</td></tr>
          <tr><td>10</td><td>حسام طارق</td><td>المبيعات</td><td>مدير مبيعات</td></tr>
        </tbody>
      </table>
      
      <!-- نظام التحكم في الصفحات - سيظهر حتى مع 10 عناصر فقط -->
      <div class="pagination-container">
        <button class="pagination-btn prev-btn" disabled>
          <i class="fas fa-chevron-right"></i> السابق
        </button>
        
        <div class="page-numbers">
          <button class="pagination-btn page-btn active">1</button>
        </div>
        
        <button class="pagination-btn next-btn" disabled>
          التالي <i class="fas fa-chevron-left"></i>
        </button>
        
        <div class="page-info">
          عرض 1 - 10 من أصل 10 عناصر
        </div>
      </div>
    </div>

    <!-- مثال مع 25 عنصر -->
    <div class="example-section">
      <h3>مثال: جدول مع 25 عنصر</h3>
      <p>في هذه الحالة ستظهر الأزرار أيضاً حتى لو كانت صفحة واحدة فقط:</p>
      
      <!-- نظام التحكم في الصفحات -->
      <div class="pagination-container">
        <button class="pagination-btn prev-btn" disabled>
          <i class="fas fa-chevron-right"></i> السابق
        </button>
        
        <div class="page-numbers">
          <button class="pagination-btn page-btn active">1</button>
        </div>
        
        <button class="pagination-btn next-btn" disabled>
          التالي <i class="fas fa-chevron-left"></i>
        </button>
        
        <div class="page-info">
          عرض 1 - 25 من أصل 25 عنصر
        </div>
      </div>
    </div>

    <!-- مثال مع أكثر من 50 عنصر -->
    <div class="example-section">
      <h3>مثال: جدول مع 127 عنصر (3 صفحات)</h3>
      <p>في هذه الحالة ستظهر أرقام الصفحات والتنقل:</p>
      
      <!-- نظام التحكم في الصفحات -->
      <div class="pagination-container">
        <button class="pagination-btn prev-btn" disabled>
          <i class="fas fa-chevron-right"></i> السابق
        </button>
        
        <div class="page-numbers">
          <button class="pagination-btn page-btn active">1</button>
          <button class="pagination-btn page-btn">2</button>
          <button class="pagination-btn page-btn">3</button>
        </div>
        
        <button class="pagination-btn next-btn">
          التالي <i class="fas fa-chevron-left"></i>
        </button>
        
        <div class="page-info">
          عرض 1 - 50 من أصل 127 عنصر
        </div>
      </div>
    </div>

    <!-- ملخص التحديثات -->
    <div class="example-section">
      <h3>ملخص التحديثات المطبقة</h3>
      <table class="test-table">
        <thead>
          <tr>
            <th>الملف</th>
            <th>الدالة المحدثة</th>
            <th>التغيير</th>
            <th>النتيجة</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>script.js</td>
            <td>updatePaginationControls</td>
            <td>totalEmployees === 0 بدلاً من totalPages <= 1</td>
            <td>✅ تظهر الأزرار دائماً</td>
          </tr>
          <tr>
            <td>vacations.js</td>
            <td>updatePaginationControlsAddedVacations</td>
            <td>totalAddedVacations === 0 بدلاً من totalPages <= 1</td>
            <td>✅ تظهر الأزرار دائماً</td>
          </tr>
          <tr>
            <td>vacations.js</td>
            <td>updatePaginationControlsVacations</td>
            <td>totalVacations === 0 بدلاً من totalPages <= 1</td>
            <td>✅ تظهر الأزرار دائماً</td>
          </tr>
          <tr>
            <td>extraHours.js</td>
            <td>updatePaginationControlsExtraHours</td>
            <td>totalExtraHours === 0 بدلاً من totalPages <= 1</td>
            <td>✅ تظهر الأزرار دائماً</td>
          </tr>
          <tr>
            <td>extraHours.js</td>
            <td>updatePaginationControlsReports</td>
            <td>totalReports === 0 بدلاً من totalPages <= 1</td>
            <td>✅ تظهر الأزرار دائماً</td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- تعليمات الاختبار -->
    <div class="example-section">
      <h3>كيفية الاختبار</h3>
      <ol>
        <li>افتح أي من الصفحات: index.html، vacations.html، أو extraHours.html</li>
        <li>حتى لو كان لديك أقل من 50 عنصر، ستظهر أزرار الصفحات</li>
        <li>الأزرار ستكون معطلة (رمادية) إذا لم تكن قابلة للاستخدام</li>
        <li>رقم الصفحة "1" سيظهر دائماً</li>
        <li>معلومات الصفحة ستظهر العدد الصحيح للعناصر</li>
      </ol>
    </div>
  </div>

  <script>
    console.log('✅ تم تحديث جميع أنظمة الصفحات لإظهار الأزرار دائماً');
    console.log('الأزرار ستظهر حتى مع البيانات القليلة (أقل من 50 صف)');
  </script>
</body>
</html>
