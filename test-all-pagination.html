<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>اختبار جميع أنظمة الصفحات</title>
  <link rel="stylesheet" href="shared-styles.css">
  <link rel="stylesheet" href="pagination-styles.css">
  <style>
    body {
      padding: 20px;
      background: #f5f5f5;
    }
    .test-container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      margin-bottom: 30px;
    }
    .test-info {
      background: #e3f2fd;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
      border-left: 4px solid #2196f3;
    }
    .test-section {
      margin-bottom: 40px;
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px;
    }
    .test-section h3 {
      color: #2196f3;
      margin-bottom: 15px;
    }
    .test-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    .test-table th,
    .test-table td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: center;
    }
    .test-table th {
      background: #f5f5f5;
      font-weight: bold;
    }
    .status-success {
      color: #4CAF50;
      font-weight: bold;
    }
    .status-error {
      color: #f44336;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1>اختبار جميع أنظمة الصفحات</h1>
    
    <div class="test-info">
      <h3>ملخص التحديثات المطبقة:</h3>
      <ul>
        <li>✅ جدول قاعدة بيانات الموظفين - تم تطبيق نظام الصفحات</li>
        <li>✅ جدول الإجازات المضافة - تم تطبيق نظام الصفحات</li>
        <li>✅ جدول عرض الإجازات - تم تطبيق نظام الصفحات</li>
        <li>✅ جدول الساعات الإضافية المضافة - تم تطبيق نظام الصفحات</li>
        <li>✅ جدول تقارير الساعات الإضافية - تم تطبيق نظام الصفحات</li>
        <li>✅ تنسيقات CSS منفصلة لتجنب التعارض</li>
        <li>✅ نص أبيض في الأزرار النشطة كما طُلب</li>
      </ul>
    </div>

    <!-- اختبار نظام الصفحات للموظفين -->
    <div class="test-section">
      <h3>1. نظام الصفحات لجدول الموظفين</h3>
      <p><strong>الملف:</strong> index.html</p>
      <p><strong>الوصف:</strong> 50 موظف لكل صفحة مع أرقام الصفحات والتنقل</p>
      <p><strong>المميزات:</strong></p>
      <ul>
        <li>عرض 50 سطر لكل صفحة</li>
        <li>أزرار السابق والتالي</li>
        <li>أرقام الصفحات التفاعلية</li>
        <li>معلومات الصفحة الحالية</li>
        <li>إعادة تعيين الصفحة عند البحث</li>
      </ul>
      <div class="status-success">✅ تم التطبيق بنجاح</div>
    </div>

    <!-- اختبار نظام الصفحات للإجازات -->
    <div class="test-section">
      <h3>2. نظام الصفحات لجداول الإجازات</h3>
      <p><strong>الملف:</strong> vacations.html</p>
      <p><strong>الجداول المحدثة:</strong></p>
      <ul>
        <li><strong>جدول الإجازات المضافة:</strong> ID = addedVacationsTableBody</li>
        <li><strong>جدول عرض الإجازات:</strong> ID = vacationsTableBody</li>
      </ul>
      <p><strong>المتغيرات المضافة:</strong></p>
      <ul>
        <li>currentPageAddedVacations, itemsPerPageAddedVacations</li>
        <li>currentPageVacations, itemsPerPageVacations</li>
      </ul>
      <div class="status-success">✅ تم التطبيق بنجاح</div>
    </div>

    <!-- اختبار نظام الصفحات للساعات الإضافية -->
    <div class="test-section">
      <h3>3. نظام الصفحات لجداول الساعات الإضافية</h3>
      <p><strong>الملف:</strong> extraHours.html</p>
      <p><strong>الجداول المحدثة:</strong></p>
      <ul>
        <li><strong>جدول الساعات الإضافية:</strong> ID = extraHoursTableBody</li>
        <li><strong>جدول التقارير:</strong> ID = reportsTableBody</li>
      </ul>
      <p><strong>المتغيرات المضافة:</strong></p>
      <ul>
        <li>currentPageExtraHours, itemsPerPageExtraHours</li>
        <li>currentPageReports, itemsPerPageReports</li>
      </ul>
      <div class="status-success">✅ تم التطبيق بنجاح</div>
    </div>

    <!-- اختبار التنسيقات -->
    <div class="test-section">
      <h3>4. تنسيقات CSS</h3>
      <p><strong>الملف:</strong> pagination-styles.css</p>
      <p><strong>المميزات:</strong></p>
      <ul>
        <li>تصميم موحد لجميع الجداول</li>
        <li>نص أبيض في الأزرار النشطة</li>
        <li>تصميم متجاوب للشاشات الصغيرة</li>
        <li>تأثيرات بصرية ناعمة</li>
        <li>تجنب التعارض مع التنسيقات الموجودة</li>
      </ul>
      <div class="status-success">✅ تم التطبيق بنجاح</div>
    </div>

    <!-- جدول ملخص الوظائف -->
    <div class="test-section">
      <h3>5. ملخص الدوال المضافة</h3>
      <table class="test-table">
        <thead>
          <tr>
            <th>الجدول</th>
            <th>دالة العرض</th>
            <th>دالة التحكم</th>
            <th>دالة التنقل</th>
            <th>الحالة</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>جدول الموظفين</td>
            <td>displayResults</td>
            <td>updatePaginationControls</td>
            <td>changePage</td>
            <td class="status-success">✅</td>
          </tr>
          <tr>
            <td>الإجازات المضافة</td>
            <td>displayAddedVacations</td>
            <td>updatePaginationControlsAddedVacations</td>
            <td>changePageAddedVacations</td>
            <td class="status-success">✅</td>
          </tr>
          <tr>
            <td>عرض الإجازات</td>
            <td>displayVacations</td>
            <td>updatePaginationControlsVacations</td>
            <td>changePageVacations</td>
            <td class="status-success">✅</td>
          </tr>
          <tr>
            <td>الساعات الإضافية</td>
            <td>displayExtraHours</td>
            <td>updatePaginationControlsExtraHours</td>
            <td>changePageExtraHours</td>
            <td class="status-success">✅</td>
          </tr>
          <tr>
            <td>تقارير الساعات</td>
            <td>displayReports</td>
            <td>updatePaginationControlsReports</td>
            <td>changePageReports</td>
            <td class="status-success">✅</td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- تعليمات الاختبار -->
    <div class="test-section">
      <h3>6. تعليمات الاختبار</h3>
      <ol>
        <li>افتح صفحة قاعدة بيانات الموظفين (index.html) وتحقق من ظهور نظام الصفحات</li>
        <li>افتح صفحة الإجازات (vacations.html) وتحقق من الجدولين</li>
        <li>افتح صفحة الساعات الإضافية (extraHours.html) وتحقق من الجدولين</li>
        <li>اختبر التنقل بين الصفحات</li>
        <li>اختبر البحث والفلترة مع إعادة تعيين الصفحة</li>
        <li>تحقق من التصميم المتجاوب على الشاشات الصغيرة</li>
      </ol>
    </div>

    <!-- محاكاة نظام الصفحات -->
    <div class="test-section">
      <h3>7. محاكاة نظام الصفحات</h3>
      <p>مثال على شكل نظام الصفحات:</p>
      
      <!-- نظام التحكم في الصفحات -->
      <div class="pagination-container">
        <button class="pagination-btn prev-btn" disabled>
          <i class="fas fa-chevron-right"></i> السابق
        </button>
        
        <div class="page-numbers">
          <button class="pagination-btn page-btn active">1</button>
          <button class="pagination-btn page-btn">2</button>
          <button class="pagination-btn page-btn">3</button>
          <span class="pagination-dots">...</span>
          <button class="pagination-btn page-btn">10</button>
        </div>
        
        <button class="pagination-btn next-btn">
          التالي <i class="fas fa-chevron-left"></i>
        </button>
        
        <div class="page-info">
          عرض 1 - 50 من أصل 500 عنصر
        </div>
      </div>
    </div>
  </div>

  <script>
    console.log('تم تحميل صفحة اختبار أنظمة الصفحات بنجاح');
    console.log('جميع الأنظمة جاهزة للاختبار');
  </script>
</body>
</html>
