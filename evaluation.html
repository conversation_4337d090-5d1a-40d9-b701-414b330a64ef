<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>قسم التقييم</title>
  <link rel="stylesheet" href="style.css" />
  <link rel="stylesheet" href="shared-styles.css">
  <link rel="stylesheet" href="evaluation.css">
  <link rel="stylesheet" href="pagination-styles.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="no-sidebar-layout.css">
  <script src="permissions.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body class="evaluation-page">

    <!-- زر العودة للوحة التحكم -->
  <div class="back-to-dashboard">
    <a href="evaluation-cards.html" class="dashboard-btn">
      <i class="fas fa-arrow-right"></i>
      <span>العودة لإدارة التقييم</span>
    </a>
  </div>




  <div class="main-content full-width" id="mainContent">
    <h1>قسم التقييم</h1>

    <div class="tab-content" id="add-evaluation" style="display: none;">
      <div class="evaluation-form">
        <div class="form-group">
          <label for="evaluationEmployeeSearch">البحث عن الموظف (بالاسم أو الكود):</label>
          <input type="text" id="evaluationEmployeeSearch" class="employee-search-input" placeholder="أدخل اسم أو كود الموظف" list="evaluationEmployeeSearchSuggestions" autocomplete="off">
          <datalist id="evaluationEmployeeSearchSuggestions"></datalist>
        </div>
        <div class="form-group">
          <label for="evaluationEmployeeCode">كود الموظف:</label>
          <input type="text" id="evaluationEmployeeCode" readonly>
        </div>
        <div class="form-group">
          <label for="evaluationEmployeeName">اسم الموظف:</label>
          <input type="text" id="evaluationEmployeeName" readonly>
        </div>
        <div class="form-group">
          <label for="evaluationEmployeeDepartment">الإدارة:</label>
          <input type="text" id="evaluationEmployeeDepartment" readonly>
        </div>
        <div class="form-group">
          <label for="evaluationType">نوع التقييم:</label>
          <select id="evaluationType" required>
            <option value="">اختر نوع التقييم</option>
            <option value="شهري">شهري</option>
            <option value="ربع سنوي">ربع سنوي</option>
          </select>
        </div>
        <div class="form-group">
          <label for="evaluationStartDate">تاريخ بداية التقييم:</label>
          <input type="date" id="evaluationStartDate" required>
        </div>
        <div class="form-group">
          <label for="evaluationEndDate">تاريخ نهاية التقييم:</label>
          <input type="date" id="evaluationEndDate" required>
        </div>
        <div class="form-group">
          <label for="evaluationScore">درجة التقييم:</label>
          <input type="number" id="evaluationScore" min="0" max="100" step="0.01" required> <span>%</span>
        </div>
        <div class="form-group">
          <label for="evaluationNotes">ملاحظات:</label>
          <textarea id="evaluationNotes" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
        </div>
        <div class="form-actions">
          <button id="saveEvaluation" class="save-btn">حفظ التقييم</button>
          <button id="resetEvaluationForm" class="reset-btn">إعادة تعيين</button>
        </div>
      </div>
      <div class="evaluation-table-container">
        <h3>قائمة التقييمات</h3>

        <!-- فلاتر البحث المحددة للتقييمات -->
        <div class="search-filters-container">
          <div class="search-filters-row">
            <div class="form-group">
              <label for="filterEvaluationEmployeeCode">كود الموظف:</label>
              <input type="text" id="filterEvaluationEmployeeCode" placeholder="أدخل كود الموظف" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterEvaluationEmployeeName">اسم الموظف:</label>
              <input type="text" id="filterEvaluationEmployeeName" placeholder="أدخل اسم الموظف" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterEvaluationFromDate">من تاريخ:</label>
              <input type="date" id="filterEvaluationFromDate" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterEvaluationToDate">إلى تاريخ:</label>
              <input type="date" id="filterEvaluationToDate" class="filter-input">
            </div>
          </div>

          <div class="filter-actions">
            <button id="applyEvaluationFiltersBtn" class="search-btn">تطبيق الفلاتر</button>
            <button id="clearEvaluationFiltersBtn" class="reset-btn">مسح الفلاتر</button>
            <button id="exportEvaluationsBtn" class="export-btn">تصدير إلى Excel</button>
          </div>
        </div>
        <table class="evaluation-table" id="evaluation-table">
          <thead>
            <tr>
              <th>كود الموظف</th>
              <th>اسم الموظف</th>
              <th>الإدارة</th>
              <th>نوع التقييم</th>
              <th>تاريخ البداية</th>
              <th>تاريخ النهاية</th>
              <th>درجة التقييم</th>
              <th>ملاحظات</th>
              <th>إجراءات</th>
            </tr>
          </thead>
          <tbody>
            <!-- سيتم تعبئة الصفوف ديناميكياً -->
          </tbody>
        </table>

        <!-- نظام التحكم في الصفحات -->
        <div class="pagination-container" id="paginationContainerEvaluations">
          <button class="pagination-btn prev-btn" id="prevBtnEvaluations">
            <i class="fas fa-chevron-right"></i> السابق
          </button>

          <div class="page-numbers" id="pageNumbersEvaluations">
            <!-- سيتم إضافة أرقام الصفحات ديناميكياً -->
          </div>

          <button class="pagination-btn next-btn" id="nextBtnEvaluations">
            التالي <i class="fas fa-chevron-left"></i>
          </button>

          <div class="page-info" id="pageInfoEvaluations">
            <!-- سيتم عرض معلومات الصفحة هنا -->
          </div>
        </div>
      </div>
    </div>
    <div class="tab-content" id="evaluation-reports" style="display:none;">
      <div class="unified-filters">
        <div class="filter-row">
          <div class="form-group">
            <label for="reportStartDate">من تاريخ</label>
            <input type="date" id="reportStartDate">
          </div>
          <div class="form-group">
            <label for="reportEndDate">إلى تاريخ</label>
            <input type="date" id="reportEndDate">
          </div>
          <div class="form-group">
            <label for="reportDepartment">الإدارة</label>
            <select id="reportDepartment">
              <option value="">كل الإدارات</option>
            </select>
          </div>
          <div class="form-group">
            <label for="reportType">نوع التقييم</label>
            <select id="reportType">
              <option value="">كل الأنواع</option>
              <option value="شهري">شهري</option>
              <option value="ربع سنوي">ربع سنوي</option>
            </select>
          </div>
          <div class="form-group">
            <label for="reportMinScore">الحد الأدنى للدرجة</label>
            <input type="number" id="reportMinScore" min="0" max="100" step="0.01" placeholder="0">
          </div>
          <div class="form-group">
            <label for="reportMaxScore">الحد الأقصى للدرجة</label>
            <input type="number" id="reportMaxScore" min="0" max="100" step="0.01" placeholder="100">
          </div>
        </div>
        <div class="filter-actions">
          <button id="applyReportFilters" class="search-btn">تطبيق الفلاتر</button>
          <button id="resetReportFilters" class="reset-btn">مسح الفلاتر</button>
        </div>
      </div>
      <div class="reports-container">
        <div class="report-card" id="total-evaluations-report">
          <div class="report-icon"><i class="fas fa-chart-bar"></i></div>
          <div class="report-value" id="totalEvaluationsValue">0</div>
          <div class="report-footer">إجمالي التقييمات</div>
        </div>
        <div class="report-card" id="average-score-report">
          <div class="report-icon"><i class="fas fa-percentage"></i></div>
          <div class="report-value" id="averageScoreValue">0%</div>
          <div class="report-footer">متوسط درجة التقييم</div>
        </div>
        <div class="report-card" id="top-employee-report">
          <div class="report-icon"><i class="fas fa-trophy"></i></div>
          <div class="report-value" id="topEmployeeValue">-</div>
          <div class="report-footer">أعلى موظف تقييمًا</div>
        </div>
      </div>
      <div class="reports-tables">
        <div class="table-controls">
          <input type="text" id="searchReportTable" placeholder="بحث في التقارير...">
          <button id="exportReportTableBtn" class="export-btn">تصدير إلى Excel</button>
        </div>
        <table class="report-table" id="evaluation-report-table">
          <thead>
            <tr>
              <th>كود الموظف</th>
              <th>اسم الموظف</th>
              <th class="department-column">الإدارة</th>
              <th>نوع التقييم</th>
              <th>تاريخ البداية</th>
              <th>تاريخ النهاية</th>
              <th>درجة التقييم</th>
              <th class="notes-column">ملاحظات</th>
            </tr>
          </thead>
          <tbody>
            <!-- سيتم تعبئة الصفوف ديناميكياً -->
          </tbody>
        </table>
      </div>
    </div>

    <!-- تبويب أعلى وأقل التقييمات -->
    <div class="tab-content" id="top-bottom-evaluations" style="display:none;">
      <div class="unified-filters">
        <div class="filter-row">
          <div class="form-group">
            <label for="topBottomStartDate">من تاريخ</label>
            <input type="date" id="topBottomStartDate">
          </div>
          <div class="form-group">
            <label for="topBottomEndDate">إلى تاريخ</label>
            <input type="date" id="topBottomEndDate">
          </div>
          <div class="form-group">
            <label for="topBottomDepartment">الإدارة</label>
            <select id="topBottomDepartment">
              <option value="">كل الإدارات</option>
            </select>
          </div>
          <div class="form-group">
            <label for="topBottomType">نوع التقييم</label>
            <select id="topBottomType">
              <option value="">كل الأنواع</option>
              <option value="شهري">شهري</option>
              <option value="ربع سنوي">ربع سنوي</option>
            </select>
          </div>
          <div class="form-group">
            <label for="sortOrder">ترتيب النتائج</label>
            <select id="sortOrder">
              <option value="desc">من الأعلى إلى الأقل</option>
              <option value="asc">من الأقل إلى الأعلى</option>
            </select>
          </div>
          <div class="form-group">
            <label for="limitResults">عدد النتائج</label>
            <select id="limitResults">
              <option value="10">أفضل 10</option>
              <option value="20">أفضل 20</option>
              <option value="50">أفضل 50</option>
              <option value="all">جميع النتائج</option>
            </select>
          </div>
        </div>
        <div class="filter-actions">
          <button id="applyTopBottomFilters" class="search-btn">تطبيق الفلاتر</button>
          <button id="resetTopBottomFilters" class="reset-btn">مسح الفلاتر</button>
        </div>
      </div>
      <div class="reports-tables">
        <div class="table-controls">
          <input type="text" id="searchTopBottomTable" placeholder="بحث في التقييمات...">
          <button id="exportTopBottomBtn" class="export-btn">تصدير إلى Excel</button>
        </div>
        <table class="report-table" id="top-bottom-table">
          <thead>
            <tr>
              <th>الترتيب</th>
              <th>كود الموظف</th>
              <th>اسم الموظف</th>
              <th>الإدارة</th>
              <th>نوع التقييم</th>
              <th>تاريخ البداية</th>
              <th>تاريخ النهاية</th>
              <th>درجة التقييم</th>
              <th>ملاحظات</th>
            </tr>
          </thead>
          <tbody>
            <!-- سيتم تعبئة الصفوف ديناميكياً -->
          </tbody>
        </table>
      </div>
    </div>

    <!-- تبويب الموظفين غير المقيمين -->
    <div class="tab-content" id="unevaluated-employees" style="display:none;">
      <div class="unified-filters">
        <div class="filter-row">
          <div class="form-group">
            <label for="unevaluatedDepartment">الإدارة</label>
            <select id="unevaluatedDepartment">
              <option value="">كل الإدارات</option>
            </select>
          </div>
          <div class="form-group">
            <label for="unevaluatedEvaluationType">نوع التقييم</label>
            <select id="unevaluatedEvaluationType">
              <option value="">كل الأنواع</option>
              <option value="شهري">شهري</option>
              <option value="ربع سنوي">ربع سنوي</option>
            </select>
          </div>
          <div class="form-group">
            <label for="unevaluatedPeriod">الفترة المطلوبة</label>
            <select id="unevaluatedPeriod">
              <option value="current_month">الشهر الحالي</option>
              <option value="last_month">الشهر الماضي</option>
              <option value="current_quarter">الربع الحالي</option>
              <option value="last_quarter">الربع الماضي</option>
              <option value="custom">فترة مخصصة</option>
            </select>
          </div>
          <div class="form-group" id="customPeriodGroup" style="display:none;">
            <label for="customStartDate">من تاريخ</label>
            <input type="date" id="customStartDate">
          </div>
          <div class="form-group" id="customPeriodGroupEnd" style="display:none;">
            <label for="customEndDate">إلى تاريخ</label>
            <input type="date" id="customEndDate">
          </div>
        </div>
        <div class="filter-actions">
          <button id="applyUnevaluatedFilters" class="search-btn">تطبيق الفلاتر</button>
          <button id="resetUnevaluatedFilters" class="reset-btn">مسح الفلاتر</button>
        </div>
      </div>
      <div class="reports-container">
        <div class="report-card" id="unevaluated-count-report">
          <div class="report-icon"><i class="fas fa-user-times"></i></div>
          <div class="report-value" id="unevaluatedCountValue">0</div>
          <div class="report-footer">عدد الموظفين غير المقيمين</div>
        </div>
        <div class="report-card" id="total-employees-report">
          <div class="report-icon"><i class="fas fa-users"></i></div>
          <div class="report-value" id="totalEmployeesValue">0</div>
          <div class="report-footer">إجمالي الموظفين</div>
        </div>
        <div class="report-card" id="evaluation-coverage-report">
          <div class="report-icon"><i class="fas fa-chart-pie"></i></div>
          <div class="report-value" id="evaluationCoverageValue">0%</div>
          <div class="report-footer">نسبة التغطية</div>
        </div>
      </div>
      <div class="reports-tables">
        <div class="table-controls">
          <input type="text" id="searchUnevaluatedTable" placeholder="بحث في الموظفين...">
          <button id="exportUnevaluatedBtn" class="export-btn">تصدير إلى Excel</button>
        </div>
        <table class="report-table" id="unevaluated-employees-table">
          <thead>
            <tr>
              <th>كود الموظف</th>
              <th>اسم الموظف</th>
              <th>الإدارة</th>
              <th>المنصب</th>
              <th>تاريخ التوظيف</th>
              <th>آخر تقييم</th>
              <th>المدة منذ آخر تقييم</th>
            </tr>
          </thead>
          <tbody>
            <!-- سيتم تعبئة الصفوف ديناميكياً -->
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- نافذة منبثقة لتعديل التقييم -->
  <div id="editEvaluationModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>تعديل التقييم</h2>
        <span class="close" id="closeEditEvaluationModal">&times;</span>
      </div>
      <div class="modal-body">
        <div class="evaluation-form">
          <div class="form-group">
            <label for="editEvaluationEmployeeCode">كود الموظف:</label>
            <input type="text" id="editEvaluationEmployeeCode" readonly>
          </div>

          <div class="form-group">
            <label for="editEvaluationEmployeeName">اسم الموظف:</label>
            <input type="text" id="editEvaluationEmployeeName" readonly>
          </div>

          <div class="form-group">
            <label for="editEvaluationEmployeeDepartment">الإدارة:</label>
            <input type="text" id="editEvaluationEmployeeDepartment" readonly>
          </div>

          <div class="form-group">
            <label for="editEvaluationType">نوع التقييم:</label>
            <select id="editEvaluationType" required>
              <option value="">اختر نوع التقييم</option>
              <option value="شهري">شهري</option>
              <option value="ربع سنوي">ربع سنوي</option>
            </select>
          </div>

          <div class="form-group">
            <label for="editEvaluationStartDate">تاريخ بداية التقييم:</label>
            <input type="date" id="editEvaluationStartDate" required>
          </div>

          <div class="form-group">
            <label for="editEvaluationEndDate">تاريخ نهاية التقييم:</label>
            <input type="date" id="editEvaluationEndDate" required>
          </div>

          <div class="form-group">
            <label for="editEvaluationScore">درجة التقييم (%):</label>
            <input type="number" id="editEvaluationScore" min="0" max="100" step="0.01" required>
          </div>

          <div class="form-group"></div> <!-- حقل فارغ للتوازن -->

          <div class="form-group full-width">
            <label for="editEvaluationNotes">ملاحظات:</label>
            <textarea id="editEvaluationNotes" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button id="updateEvaluationBtn" class="save-btn">تحديث التقييم</button>
        <button class="cancel-btn" onclick="document.getElementById('editEvaluationModal').style.display='none'">إلغاء</button>
      </div>
    </div>
  </div>

  <script src="dateUtils.js"></script>
  <script src="arabic-date-picker.js"></script>
  <script src="form-validation.js"></script>
  <script src="evaluation.js"></script>

  <script src="sidebar.js"></script>
  
</body>
</html>