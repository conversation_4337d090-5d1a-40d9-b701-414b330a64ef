// دالة للتحقق من ما إذا كان التاريخ قبل 26-6 (العام المالي السابق)
function isBeforeJune26(dateString) {
  if (!dateString) return false;

  const date = new Date(dateString);
  const currentYear = new Date().getFullYear();

  // تحديد تاريخ 26-6 للسنة الحالية
  const june26ThisYear = new Date(currentYear, 5, 26); // الشهر 5 = يونيو (0-based)

  // إذا كان التاريخ قبل 26-6 من السنة الحالية
  return date < june26ThisYear;
}

// دالة تعيين التواريخ الافتراضية (من 26-6 إلى 25-6)
function setDefaultDateRange() {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();

  // تحديد سنة الإجازة (من 26 يونيو إلى 25 يونيو)
  let vacationYear = currentYear;
  if (currentDate.getMonth() < 5 || (currentDate.getMonth() === 5 && currentDate.getDate() < 26)) {
    // إذا كنا قبل 26 يونيو، فنحن في سنة الإجازة السابقة
    vacationYear = currentYear - 1;
  }

  // تعيين التواريخ الافتراضية
  const startDate = `${vacationYear}-06-26`;
  const endDate = `${vacationYear + 1}-06-25`;

  // تعيين التواريخ في حقول التقارير
  const reportStartDate = document.getElementById('reportStartDate');
  const reportEndDate = document.getElementById('reportEndDate');

  if (reportStartDate) {
    reportStartDate.value = startDate;
  }

  if (reportEndDate) {
    reportEndDate.value = endDate;
  }

  console.log(`تم تعيين التواريخ الافتراضية: من ${startDate} إلى ${endDate}`);
}

document.addEventListener('DOMContentLoaded', function() {
  // المتغيرات العامة
  let API_URL = localStorage.getItem('serverUrl') || "http://localhost:5500/api";
  let departments = [];
  let employees = [];

  // متغيرات نظام الصفحات للمكافآت
  let currentPageRewards = 1;
  const itemsPerPageRewards = 50;
  let totalRewards = 0;
  let filteredRewardsData = []; // لحفظ النتائج المفلترة

  // متغيرات نظام الصفحات للخصومات
  let currentPageDeductions = 1;
  const itemsPerPageDeductions = 50;
  let totalDeductions = 0;
  let filteredDeductionsData = []; // لحفظ النتائج المفلترة
  let rewards = [];
  let deductions = [];

  // متغيرات التقارير
  let filteredRewards = [];
  let filteredDeductions = [];
  let currentDetailsData = [];
  let currentDetailsType = '';
  
  // عناصر DOM للمكافآت
  const employeeSearchReward = document.getElementById('employeeSearchReward');
  const employeeCodeReward = document.getElementById('employeeCodeReward');
  const employeeNameReward = document.getElementById('employeeNameReward');
  const employeeDepartmentReward = document.getElementById('employeeDepartmentReward');
  const rewardAmount = document.getElementById('rewardAmount');
  const rewardReason = document.getElementById('rewardReason');
  const rewardDate = document.getElementById('rewardDate');
  const rewardNotes = document.getElementById('rewardNotes');
  const saveRewardBtn = document.getElementById('saveReward');
  const resetRewardFormBtn = document.getElementById('resetRewardForm');
  
  // عناصر DOM للخصومات
  const employeeSearchDeduction = document.getElementById('employeeSearchDeduction');
  const employeeCodeDeduction = document.getElementById('employeeCodeDeduction');
  const employeeNameDeduction = document.getElementById('employeeNameDeduction');
  const employeeDepartmentDeduction = document.getElementById('employeeDepartmentDeduction');
  const deductionAmount = document.getElementById('deductionAmount');
  const deductionReason = document.getElementById('deductionReason');
  const deductionDate = document.getElementById('deductionDate');
  const deductionNotes = document.getElementById('deductionNotes');
  const saveDeductionBtn = document.getElementById('saveDeduction');
  const resetDeductionFormBtn = document.getElementById('resetDeductionForm');
  
  // عناصر DOM للتبويبات
  const tabBtns = document.querySelectorAll('.tab-btn');
  const tabContents = document.querySelectorAll('.tab-content');

  // عناصر DOM للتقارير
  const totalRewardsEl = document.getElementById('totalRewards');
  const totalDeductionsEl = document.getElementById('totalDeductions');
  const totalRewardsAndDeductionsEl = document.getElementById('totalRewardsAndDeductions');
  const totalCountEl = document.getElementById('totalCount');
  const netAmountEl = document.getElementById('netAmount');
  const rewardsCountEl = document.getElementById('rewardsCount');
  const deductionsCountEl = document.getElementById('deductionsCount');
  const reportEmployeeSearch = document.getElementById('reportEmployeeSearch');
  const reportDepartmentFilter = document.getElementById('reportDepartmentFilter');
  const reportStartDate = document.getElementById('reportStartDate');
  const reportEndDate = document.getElementById('reportEndDate');
  const applyReportFiltersBtn = document.getElementById('applyReportFilters');
  const clearReportFiltersBtn = document.getElementById('clearReportFilters');
  const topRewardsTableBody = document.getElementById('topRewardsTableBody');
  const topDeductionsTableBody = document.getElementById('topDeductionsTableBody');
  const detailsModal = document.getElementById('detailsModal');
  const detailsModalTitle = document.getElementById('detailsModalTitle');
  const detailsTableBody = document.getElementById('detailsTableBody');
  const detailsTotalCount = document.getElementById('detailsTotalCount');
  const detailsTotalAmount = document.getElementById('detailsTotalAmount');
  
  // عناصر DOM للجداول والفلاتر
  const departmentFilterRewards = document.getElementById('departmentFilterRewards');
  const departmentFilterDeductions = document.getElementById('departmentFilterDeductions');
  const startDateRewards = document.getElementById('startDateRewards');
  const endDateRewards = document.getElementById('endDateRewards');
  const startDateDeductions = document.getElementById('startDateDeductions');
  const endDateDeductions = document.getElementById('endDateDeductions');
  const searchRewardsBtn = document.getElementById('searchRewardsBtn');
  const resetRewardsBtn = document.getElementById('resetRewardsBtn');
  const exportRewardsBtn = document.getElementById('exportRewardsBtn');
  const searchDeductionsBtn = document.getElementById('searchDeductionsBtn');
  const resetDeductionsBtn = document.getElementById('resetDeductionsBtn');
  const exportDeductionsBtn = document.getElementById('exportDeductionsBtn');
  const rewardsTableBody = document.getElementById('rewardsTableBody');
  const deductionsTableBody = document.getElementById('deductionsTableBody');
  
  // عناصر DOM للمودال
  const editModal = document.getElementById('editModal');
  const editModalTitle = document.getElementById('editModalTitle');
  const editForm = document.getElementById('editForm');
  const editId = document.getElementById('editId');
  const editType = document.getElementById('editType');
  const editEmployeeCode = document.getElementById('editEmployeeCode');
  const editEmployeeName = document.getElementById('editEmployeeName');
  const editEmployeeDepartment = document.getElementById('editEmployeeDepartment');
  const editAmount = document.getElementById('editAmount');
  const editReason = document.getElementById('editReason');
  const editDate = document.getElementById('editDate');
  const editNotes = document.getElementById('editNotes');
  const editAmountLabel = document.getElementById('editAmountLabel');
  const editReasonLabel = document.getElementById('editReasonLabel');
  const editDateLabel = document.getElementById('editDateLabel');
  const saveEditBtn = document.getElementById('saveEdit');
  const closeModalBtn = document.querySelector('.close');
  const cancelBtn = document.querySelector('.cancel-btn');

  // التحقق من الصلاحيات
  function checkPermissions() {
    const permissions = JSON.parse(localStorage.getItem('permissions') || '{}');

    // التحقق من صلاحية الوصول للصفحة - يحتاج إما صلاحية عامة أو صلاحية محددة
    const hasGeneralAccess = permissions.view_rewards_deductions;
    const hasSpecificAccess = permissions.view_rewards_list || permissions.view_deductions_list ||
                             permissions.add_reward || permissions.add_deduction;

    if (!hasGeneralAccess && !hasSpecificAccess) {
      alert('ليس لديك صلاحية للوصول إلى هذه الصفحة');
      window.location.href = 'dashboard.html';
      return false;
    }

    // التحقق من صلاحيات التقارير
    const selectedContent = localStorage.getItem('selectedRewardsDeductionsTab');
    if (selectedContent === 'reports') {
      if (!permissions.view_rewards_deductions_reports) {
        alert('ليس لديك صلاحية لعرض التقارير');
        window.location.href = 'rewards-deductions-cards.html';
        return false;
      }
    }

    return true;
  }

  // تحميل البيانات الأولية
  async function loadData() {
    try {
      console.log(`محاولة تحميل البيانات من: ${API_URL}`);
      
      await loadDepartments();
      await loadEmployees();
      await loadRewards();
      await loadDeductions();

      // تحديث بيانات التقارير
      updateReportsData();

      return true;
    } catch (error) {
      console.error('فشل في تحميل البيانات:', error);
      alert('فشل في تحميل البيانات: ' + error.message);
      return false;
    }
  }
  
  // تحميل الإدارات باستخدام النظام الموحد
  async function loadDepartments() {
    try {
      if (window.SharedUtils && window.SharedUtils.loadDepartments) {
        departments = await window.SharedUtils.loadDepartments();
        console.log('تم تحميل الإدارات بنجاح من النظام الموحد:', departments);
        populateDepartmentSelects();
      } else {
        // fallback للكود القديم
        const response = await fetch(`${API_URL}/departments`);
        if (response.ok) {
          departments = await response.json();
          populateDepartmentSelects();
        } else {
          throw new Error(`فشل في تحميل الإدارات: ${response.status}`);
        }
      }
    } catch (error) {
      console.error('خطأ في تحميل الإدارات:', error);
      throw error;
    }
  }
  
  // تحميل الموظفين
  async function loadEmployees() {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/employees`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (response.ok) {
        employees = await response.json();
        setupEmployeeSearch();
      } else {
        throw new Error(`فشل في تحميل الموظفين: ${response.status}`);
      }
    } catch (error) {
      console.error('خطأ في تحميل الموظفين:', error);
      throw error;
    }
  }
  
  // تحميل المكافآت
  async function loadRewards() {
    try {
      const response = await fetch(`${API_URL}/rewards`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (response.ok) {
        rewards = await response.json();
        displayRewards(rewards);
        updateReportsData();
      } else {
        throw new Error(`فشل في تحميل المكافآت: ${response.status}`);
      }
    } catch (error) {
      console.error('خطأ في تحميل المكافآت:', error);
      throw error;
    }
  }
  
  // تحميل الخصومات
  async function loadDeductions() {
    try {
      const response = await fetch(`${API_URL}/deductions`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (response.ok) {
        deductions = await response.json();
        displayDeductions(deductions);
        updateReportsData();
      } else {
        throw new Error(`فشل في تحميل الخصومات: ${response.status}`);
      }
    } catch (error) {
      console.error('خطأ في تحميل الخصومات:', error);
      throw error;
    }
  }
  
  // ملء قوائم الإدارات
  function populateDepartmentSelects() {
    const selects = [departmentFilterRewards, departmentFilterDeductions, reportDepartmentFilter];

    selects.forEach(select => {
      if (select) {
        select.innerHTML = '<option value="">كل الإدارات</option>';
        departments.forEach(dept => {
          const option = document.createElement('option');
          option.value = dept;
          option.textContent = dept;
          select.appendChild(option);
        });
      }
    });
  }
  
  // إعداد البحث عن الموظفين
  function setupEmployeeSearch() {
    // إعداد البحث للمكافآت
    if (employeeSearchReward) {
      setupEmployeeSearchInput(employeeSearchReward, 'employeeSearchSuggestionsReward', {
        code: employeeCodeReward,
        name: employeeNameReward,
        department: employeeDepartmentReward
      });
    }
    
    // إعداد البحث للخصومات
    if (employeeSearchDeduction) {
      setupEmployeeSearchInput(employeeSearchDeduction, 'employeeSearchSuggestionsDeduction', {
        code: employeeCodeDeduction,
        name: employeeNameDeduction,
        department: employeeDepartmentDeduction
      });
    }
  }
  
  // إعداد حقل البحث عن الموظف
  function setupEmployeeSearchInput(searchInput, datalistId, fields) {
    const datalist = document.getElementById(datalistId);
    
    searchInput.addEventListener('input', function() {
      const searchTerm = this.value.trim();
      
      console.log(`البحث عن الموظف: ${searchTerm}, عدد الموظفين المتاحين: ${employees.length}`);
      
      if (!searchTerm || searchTerm === '') {
        // مسح الحقول عند مسح البحث
        fields.code.value = '';
        fields.name.value = '';
        fields.department.value = '';
        datalist.innerHTML = '';
        return;
      }
      
      if (searchTerm.length < 2) {
        datalist.innerHTML = '';
        return;
      }
      
      const searchTermLower = searchTerm.toLowerCase();
      
      // البحث في الموظفين - تحسين البحث ليشمل أجزاء من الاسم
      const filteredEmployees = employees.filter(emp => {
        // التأكد من أن البيانات موجودة وتحويلها إلى نص
        const fullName = emp.full_name ? String(emp.full_name).toLowerCase() : '';
        const code = emp.code ? String(emp.code).toLowerCase() : '';
        const department = emp.department ? String(emp.department).toLowerCase() : '';
        
        // البحث في كل الحقول
        return fullName.includes(searchTermLower) || 
               code.includes(searchTermLower) || 
               department.includes(searchTermLower);
      });
      
      console.log(`نتائج البحث: ${filteredEmployees.length} موظف`);
      if (filteredEmployees.length > 0) {
        console.log('أول نتيجة:', filteredEmployees[0]);
      }
      
      // البحث عن تطابق مباشر أولاً
      const exactCodeMatch = employees.find(emp => 
        emp.code && String(emp.code).toLowerCase() === searchTermLower
      );
      const exactNameMatch = employees.find(emp => 
        emp.full_name && String(emp.full_name).toLowerCase() === searchTermLower
      );
      
      // إذا وُجد تطابق مباشر، املأ الحقول
      if (exactCodeMatch) {
        fillEmployeeFields(exactCodeMatch, fields);
      } else if (exactNameMatch) {
        fillEmployeeFields(exactNameMatch, fields);
      }
      // إذا كان هناك موظف واحد فقط في النتائج، املأ الحقول
      else if (filteredEmployees.length === 1) {
        fillEmployeeFields(filteredEmployees[0], fields);
      }
      // إذا كان البحث يبدأ بكود أو اسم موظف، املأ الحقول
      else {
        const startsWithMatch = employees.find(emp => {
          const nameLower = emp.full_name ? String(emp.full_name).toLowerCase() : '';
          const codeLower = emp.code ? String(emp.code).toLowerCase() : '';
          return nameLower.startsWith(searchTermLower) || codeLower.startsWith(searchTermLower);
        });
        
        if (startsWithMatch) {
          fillEmployeeFields(startsWithMatch, fields);
        }
      }
      
      // إضافة الخيارات للقائمة المنسدلة - تحسين عرض الاقتراحات
      datalist.innerHTML = '';
      if (filteredEmployees.length > 0) {
        // ترتيب النتائج: الأكواد أولاً ثم الأسماء
        filteredEmployees.sort((a, b) => {
          // إذا كان البحث يطابق الكود، ضع هذا الموظف في المقدمة
          const aCodeMatch = a.code && String(a.code).toLowerCase().includes(searchTermLower);
          const bCodeMatch = b.code && String(b.code).toLowerCase().includes(searchTermLower);
          
          if (aCodeMatch && !bCodeMatch) return -1;
          if (!aCodeMatch && bCodeMatch) return 1;
          
          // ثم رتب حسب الاسم
          return (a.full_name || '').localeCompare(b.full_name || '');
        });
        
        // عرض أول 15 نتيجة فقط لتحسين الأداء
        filteredEmployees.slice(0, 15).forEach(emp => {
          const option = document.createElement('option');
          option.value = `${emp.code} - ${emp.full_name}`;
          datalist.appendChild(option);
        });
      } else {
        // إضافة رسالة إذا لم يتم العثور على نتائج
        const option = document.createElement('option');
        option.value = "لا توجد نتائج مطابقة";
        datalist.appendChild(option);
      }
    });
    
    // اختيار الموظف
    searchInput.addEventListener('change', function() {
      const value = this.value;
      if (!value || value === "لا توجد نتائج مطابقة") return;
      
      // استخراج الكود من القيمة المحددة
      const codePart = value.split(' - ')[0];
      const employee = employees.find(emp => emp.code == codePart);
      
      if (employee) {
        fillEmployeeFields(employee, fields);
      }
    });
  }
  
  // وظيفة مساعدة لملء حقول الموظف
  function fillEmployeeFields(employee, fields) {
    if (employee && fields) {
      fields.code.value = employee.code || '';
      fields.name.value = employee.full_name || '';
      fields.department.value = employee.department || '';
    }
  }
  
  // إعداد التبويبات باستخدام النظام الموحد
  function setupTabs() {
    window.SharedUtils.setupUnifiedTabs();

    // إضافة مستمع للأحداث المخصصة
    document.addEventListener('tabChanged', function(e) {
      const tabId = e.detail.tabId;

      // تحديث التقارير عند الانتقال إلى تبويب التقارير
      if (tabId === 'reports') {
        updateReportsData();
      }

      // تطبيق الصلاحيات عند تغيير التبويب
      setTimeout(() => {
        applyButtonPermissions();
      }, 50);
    });
  }
  
  // حفظ مكافأة
  async function saveReward() {
    try {
      const rewardData = {
        employee_code: employeeCodeReward.value,
        employee_name: employeeNameReward.value,
        department: employeeDepartmentReward.value,
        amount: parseInt(rewardAmount.value),
        reason: rewardReason.value,
        date: rewardDate.value,
        notes: rewardNotes.value
      };
      
      // التحقق من صحة البيانات
      if (!rewardData.employee_code || !rewardData.amount || !rewardData.reason || !rewardData.date) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
      }
      
      const response = await fetch(`${API_URL}/rewards`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(rewardData)
      });
      
      if (response.ok) {
        alert('تم حفظ المكافأة بنجاح');
        resetRewardForm();
        await loadRewards();
      } else {
        const error = await response.json();
        alert('فشل في حفظ المكافأة: ' + (error.error || 'خطأ غير معروف'));
      }
    } catch (error) {
      console.error('خطأ في حفظ المكافأة:', error);
      alert('فشل في حفظ المكافأة: ' + error.message);
    }
  }
  
  // حفظ خصم
  async function saveDeduction() {
    try {
      const deductionData = {
        employee_code: employeeCodeDeduction.value,
        employee_name: employeeNameDeduction.value,
        department: employeeDepartmentDeduction.value,
        amount: parseInt(deductionAmount.value),
        reason: deductionReason.value,
        date: deductionDate.value,
        notes: deductionNotes.value
      };
      
      // التحقق من صحة البيانات
      if (!deductionData.employee_code || !deductionData.amount || !deductionData.reason || !deductionData.date) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
      }
      
      const response = await fetch(`${API_URL}/deductions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(deductionData)
      });
      
      if (response.ok) {
        alert('تم حفظ الخصم بنجاح');
        resetDeductionForm();
        await loadDeductions();
      } else {
        const error = await response.json();
        alert('فشل في حفظ الخصم: ' + (error.error || 'خطأ غير معروف'));
      }
    } catch (error) {
      console.error('خطأ في حفظ الخصم:', error);
      alert('فشل في حفظ الخصم: ' + error.message);
    }
  }
  
  // إعادة تعيين نموذج المكافأة
  function resetRewardForm() {
    employeeSearchReward.value = '';
    employeeCodeReward.value = '';
    employeeNameReward.value = '';
    employeeDepartmentReward.value = '';
    rewardAmount.value = '';
    rewardReason.value = '';
    rewardDate.value = '';
    rewardNotes.value = '';
  }
  
  // إعادة تعيين نموذج الخصم
  function resetDeductionForm() {
    employeeSearchDeduction.value = '';
    employeeCodeDeduction.value = '';
    employeeNameDeduction.value = '';
    employeeDepartmentDeduction.value = '';
    deductionAmount.value = '';
    deductionReason.value = '';
    deductionDate.value = '';
    deductionNotes.value = '';
  }
  
  // عرض المكافآت
  function displayRewards(rewardsData) {
    if (!rewardsTableBody) return;

    rewardsTableBody.innerHTML = '';

    if (rewardsData.length === 0) {
      rewardsTableBody.innerHTML = '<tr><td colspan="8">لا توجد مكافآت</td></tr>';
      updatePaginationControlsRewards();
      return;
    }

    // ترتيب البيانات حسب تاريخ الإنشاء (الأحدث أولاً)
    const sortedRewards = [...rewardsData].sort((a, b) => {
      // ترتيب حسب created_at إذا كان متوفراً، وإلا حسب id
      const aTime = a.created_at ? new Date(a.created_at).getTime() : (a.id || 0);
      const bTime = b.created_at ? new Date(b.created_at).getTime() : (b.id || 0);
      return bTime - aTime; // الأحدث أولاً
    });

    // حفظ النتائج المفلترة وتحديث العدد الإجمالي
    filteredRewardsData = sortedRewards;
    totalRewards = sortedRewards.length;

    // حساب البيانات للصفحة الحالية
    const startIndex = (currentPageRewards - 1) * itemsPerPageRewards;
    const endIndex = startIndex + itemsPerPageRewards;
    const currentPageData = sortedRewards.slice(startIndex, endIndex);

    currentPageData.forEach((reward, index) => {
      // تمييز الصف الأول في الصفحة الأولى فقط
      const isFirstInFirstPage = index === 0 && currentPageRewards === 1;
      const row = document.createElement('tr');

      // تمييز الصف الأول (آخر إضافة) بلون أخضر فاتح
      if (isFirstInFirstPage) {
        row.style.backgroundColor = '#e8f5e8';
        row.style.border = '2px solid #4CAF50';
      }

      row.innerHTML = `
        <td style="${isFirstInFirstPage ? 'font-weight: bold;' : ''}">${reward.employee_code}</td>
        <td style="${isFirstInFirstPage ? 'font-weight: bold;' : ''}">${reward.employee_name}</td>
        <td style="${isFirstInFirstPage ? 'font-weight: bold;' : ''}">${reward.department}</td>
        <td class="amount-cell reward-amount" style="${isFirstInFirstPage ? 'font-weight: bold;' : ''}">${reward.amount}</td>
        <td style="${isFirstInFirstPage ? 'font-weight: bold;' : ''}">${reward.reason}</td>
        <td style="${isFirstInFirstPage ? 'font-weight: bold;' : ''}">${formatDate(reward.reward_date || reward.date)}</td>
        <td style="${isFirstInFirstPage ? 'font-weight: bold;' : ''}">${reward.notes || '-'}</td>
        <td>
          ${hasPermission('edit_reward') ? `<button class="edit-btn" onclick="editReward(${reward.id})">تعديل</button>` : ''}
          ${hasPermission('delete_reward') ? `<button class="delete-btn" onclick="deleteReward(${reward.id})">حذف</button>` : ''}
        </td>
      `;
      rewardsTableBody.appendChild(row);
    });

    // تحديث عناصر التحكم في الصفحات
    updatePaginationControlsRewards();

    // تطبيق صلاحيات الأزرار بعد إنشاء الجدول
    applyButtonPermissions();
  }

  // فحص الصلاحيات
  function hasPermission(permission) {
    try {
      const permissions = JSON.parse(localStorage.getItem('permissions') || '{}');
      const result = permissions[permission] === true;
      console.log(`[RewardsDeductions] hasPermission(${permission}) = ${result}`, permissions);
      return result;
    } catch (error) {
      console.error('خطأ في قراءة الصلاحيات:', error);
      return false;
    }
  }

  // تطبيق صلاحيات الأزرار
  function applyButtonPermissions() {
    // استخدام دالة hasPermission العامة
    if (typeof hasPermission === 'function') {
      // إخفاء أزرار تعديل المكافآت
      if (!hasPermission('edit_reward') && !hasPermission('can_edit')) {
        document.querySelectorAll('.edit-reward-btn, [data-permission="edit_reward"]').forEach(button => {
          button.remove();
        });
      }

      // إخفاء أزرار حذف المكافآت
      if (!hasPermission('delete_reward') && !hasPermission('can_delete')) {
        document.querySelectorAll('.delete-reward-btn, [data-permission="delete_reward"]').forEach(button => {
          button.remove();
        });
      }

      // إخفاء أزرار تعديل الخصومات
      if (!hasPermission('edit_deduction') && !hasPermission('can_edit')) {
        document.querySelectorAll('.edit-penalty-btn, [data-permission="edit_deduction"]').forEach(button => {
          button.remove();
        });
      }

      // إخفاء أزرار حذف الخصومات
      if (!hasPermission('delete_deduction') && !hasPermission('can_delete')) {
        document.querySelectorAll('.delete-penalty-btn, [data-permission="delete_deduction"]').forEach(button => {
          button.remove();
        });
      }

      // تم إزالة الكود القديم لأن الأزرار الآن تُخفى مباشرة في HTML
    }
  }

  // عرض الخصومات
  function displayDeductions(deductionsData) {
    if (!deductionsTableBody) return;

    deductionsTableBody.innerHTML = '';

    if (deductionsData.length === 0) {
      deductionsTableBody.innerHTML = '<tr><td colspan="8">لا توجد خصومات</td></tr>';
      updatePaginationControlsDeductions();
      return;
    }

    // ترتيب البيانات حسب تاريخ الإنشاء (الأحدث أولاً)
    const sortedDeductions = [...deductionsData].sort((a, b) => {
      // ترتيب حسب created_at إذا كان متوفراً، وإلا حسب id
      const aTime = a.created_at ? new Date(a.created_at).getTime() : (a.id || 0);
      const bTime = b.created_at ? new Date(b.created_at).getTime() : (b.id || 0);
      return bTime - aTime; // الأحدث أولاً
    });

    // حفظ النتائج المفلترة وتحديث العدد الإجمالي
    filteredDeductionsData = sortedDeductions;
    totalDeductions = sortedDeductions.length;

    // حساب البيانات للصفحة الحالية
    const startIndex = (currentPageDeductions - 1) * itemsPerPageDeductions;
    const endIndex = startIndex + itemsPerPageDeductions;
    const currentPageData = sortedDeductions.slice(startIndex, endIndex);

    currentPageData.forEach((deduction, index) => {
      // تمييز الصف الأول في الصفحة الأولى فقط
      const isFirstInFirstPage = index === 0 && currentPageDeductions === 1;
      const row = document.createElement('tr');

      // تمييز الصف الأول (آخر إضافة) بلون أخضر فاتح
      if (isFirstInFirstPage) {
        row.style.backgroundColor = '#e8f5e8';
        row.style.border = '2px solid #4CAF50';
      }

      row.innerHTML = `
        <td style="${isFirstInFirstPage ? 'font-weight: bold;' : ''}">${deduction.employee_code}</td>
        <td style="${isFirstInFirstPage ? 'font-weight: bold;' : ''}">${deduction.employee_name}</td>
        <td style="${isFirstInFirstPage ? 'font-weight: bold;' : ''}">${deduction.department}</td>
        <td class="amount-cell deduction-amount" style="${isFirstInFirstPage ? 'font-weight: bold;' : ''}">${deduction.amount}</td>
        <td style="${isFirstInFirstPage ? 'font-weight: bold;' : ''}">${deduction.reason}</td>
        <td style="${isFirstInFirstPage ? 'font-weight: bold;' : ''}">${formatDate(deduction.date)}</td>
        <td style="${isFirstInFirstPage ? 'font-weight: bold;' : ''}">${deduction.notes || '-'}</td>
        <td>
          ${hasPermission('edit_deduction') ? `<button class="edit-btn" onclick="editDeduction(${deduction.id})">تعديل</button>` : ''}
          ${hasPermission('delete_deduction') ? `<button class="delete-btn" onclick="deleteDeduction(${deduction.id})">حذف</button>` : ''}
        </td>
      `;
      deductionsTableBody.appendChild(row);
    });

    // تحديث عناصر التحكم في الصفحات
    updatePaginationControlsDeductions();

    // تطبيق صلاحيات الأزرار بعد إنشاء الجدول
    applyButtonPermissions();
  }
  


  // استخدام دالة تنسيق التاريخ المشتركة
  function formatDate(dateString) {
    if (typeof DateUtils !== 'undefined') {
      return DateUtils.formatDateFromDatabase(dateString);
    }
    return '';
  }
  
  // تعديل مكافأة
  window.editReward = function(id) {
    // فحص صلاحية التعديل
    if (!hasPermission('edit_reward')) {
      alert('ليس لديك صلاحية لتعديل المكافآت');
      return;
    }

    const reward = rewards.find(r => r.id === id);
    if (!reward) return;
    
    editModalTitle.textContent = 'تعديل مكافأة';
    editId.value = id;
    editType.value = 'reward';
    editEmployeeCode.value = reward.employee_code;
    editEmployeeName.value = reward.employee_name;
    editEmployeeDepartment.value = reward.department;
    editAmount.value = reward.amount;
    editReason.value = reward.reason;
    // استخدام DateUtils لتنسيق التاريخ بشكل صحيح
    if (typeof DateUtils !== 'undefined') {
      editDate.value = DateUtils.formatDateForInput(reward.reward_date || reward.date);
    } else {
      editDate.value = reward.reward_date || reward.date;
    }
    editNotes.value = reward.notes || '';
    
    editAmountLabel.textContent = 'المكافأة:';
    editReasonLabel.textContent = 'سبب المكافأة:';
    editDateLabel.textContent = 'تاريخ المكافأة:';
    
    editModal.style.display = 'block';
  };
  
  // تعديل خصم
  window.editDeduction = function(id) {
    // فحص صلاحية التعديل
    if (!hasPermission('edit_deduction')) {
      alert('ليس لديك صلاحية لتعديل الخصومات');
      return;
    }

    const deduction = deductions.find(d => d.id === id);
    if (!deduction) return;
    
    editModalTitle.textContent = 'تعديل خصم';
    editId.value = id;
    editType.value = 'deduction';
    editEmployeeCode.value = deduction.employee_code;
    editEmployeeName.value = deduction.employee_name;
    editEmployeeDepartment.value = deduction.department;
    editAmount.value = deduction.amount;
    editReason.value = deduction.reason;
    // استخدام DateUtils لتنسيق التاريخ بشكل صحيح
    if (typeof DateUtils !== 'undefined') {
      editDate.value = DateUtils.formatDateForInput(deduction.date);
    } else {
      editDate.value = deduction.date;
    }
    editNotes.value = deduction.notes || '';
    
    editAmountLabel.textContent = 'الخصم:';
    editReasonLabel.textContent = 'سبب الخصم:';
    editDateLabel.textContent = 'تاريخ الخصم:';
    
    editModal.style.display = 'block';
  };
  
  // حذف مكافأة
  window.deleteReward = async function(id) {
    // فحص صلاحية الحذف
    if (!hasPermission('delete_reward')) {
      alert('ليس لديك صلاحية لحذف المكافآت');
      return;
    }

    if (!confirm('هل أنت متأكد من حذف هذه المكافأة؟')) return;
    
    try {
      const response = await fetch(`${API_URL}/rewards/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        alert('تم حذف المكافأة بنجاح');
        await loadRewards();
      } else {
        alert('فشل في حذف المكافأة');
      }
    } catch (error) {
      console.error('خطأ في حذف المكافأة:', error);
      alert('فشل في حذف المكافأة: ' + error.message);
    }
  };
  
  // حذف خصم
  window.deleteDeduction = async function(id) {
    // فحص صلاحية الحذف
    if (!hasPermission('delete_deduction')) {
      alert('ليس لديك صلاحية لحذف الخصومات');
      return;
    }

    if (!confirm('هل أنت متأكد من حذف هذا الخصم؟')) return;
    
    try {
      const response = await fetch(`${API_URL}/deductions/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        alert('تم حذف الخصم بنجاح');
        await loadDeductions();
      } else {
        alert('فشل في حذف الخصم');
      }
    } catch (error) {
      console.error('خطأ في حذف الخصم:', error);
      alert('فشل في حذف الخصم: ' + error.message);
    }
  };
  
  // حفظ التعديل
  async function saveEdit() {
    try {
      const id = editId.value;
      const type = editType.value;

      // تحويل التاريخ إلى الصيغة الصحيحة (تجنب مشاكل المنطقة الزمنية)
      const dateValue = editDate.value;

      function formatDateSafely(dateString) {
        if (!dateString) return null;
        // استخدام التاريخ مباشرة بدون تحويل لتجنب مشاكل المنطقة الزمنية
        return dateString;
      }

      const data = {
        employee_code: editEmployeeCode.value,
        employee_name: editEmployeeName.value,
        department: editEmployeeDepartment.value,
        amount: parseInt(editAmount.value),
        reason: editReason.value,
        // استخدام الحقل الصحيح حسب النوع
        [type === 'reward' ? 'reward_date' : 'date']: formatDateSafely(dateValue),
        notes: editNotes.value
      };

      console.log('📅 بيانات المكافأة/الخصم مع التاريخ المحول:', data);
      
      const endpoint = type === 'reward' ? 'rewards' : 'deductions';
      
      const response = await fetch(`${API_URL}/${endpoint}/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(data)
      });
      
      if (response.ok) {
        alert(`تم تعديل ${type === 'reward' ? 'المكافأة' : 'الخصم'} بنجاح`);
        editModal.style.display = 'none';
        if (type === 'reward') {
          await loadRewards();
        } else {
          await loadDeductions();
        }
      } else {
        alert(`فشل في تعديل ${type === 'reward' ? 'المكافأة' : 'الخصم'}`);
      }
    } catch (error) {
      console.error('خطأ في حفظ التعديل:', error);
      alert('فشل في حفظ التعديل: ' + error.message);
    }
  }
  
  // البحث في المكافآت
  async function searchRewards() {
    try {
      const params = new URLSearchParams();
      
      if (departmentFilterRewards.value) {
        params.append('department', departmentFilterRewards.value);
      }
      if (startDateRewards.value) {
        params.append('start_date', startDateRewards.value);
      }
      if (endDateRewards.value) {
        params.append('end_date', endDateRewards.value);
      }
      
      const response = await fetch(`${API_URL}/rewards/search?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const filteredRewards = await response.json();
        displayRewards(filteredRewards);
      } else {
        alert('فشل في البحث عن المكافآت');
      }
    } catch (error) {
      console.error('خطأ في البحث:', error);
      alert('فشل في البحث: ' + error.message);
    }
  }
  
  // البحث في الخصومات
  async function searchDeductions() {
    try {
      const params = new URLSearchParams();
      
      if (departmentFilterDeductions.value) {
        params.append('department', departmentFilterDeductions.value);
      }
      if (startDateDeductions.value) {
        params.append('start_date', startDateDeductions.value);
      }
      if (endDateDeductions.value) {
        params.append('end_date', endDateDeductions.value);
      }
      
      const response = await fetch(`${API_URL}/deductions/search?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const filteredDeductions = await response.json();
        displayDeductions(filteredDeductions);
      } else {
        alert('فشل في البحث عن الخصومات');
      }
    } catch (error) {
      console.error('خطأ في البحث:', error);
      alert('فشل في البحث: ' + error.message);
    }
  }
  
  // تصدير المكافآت إلى Excel
  function exportRewards() {
    const data = rewards.map(reward => ({
      'الكود': reward.employee_code,
      'اسم الموظف': reward.employee_name,
      'الإدارة': reward.department,
      'المكافأة': reward.amount,
      'سبب المكافأة': reward.reason,
      'تاريخ المكافأة': formatDate(reward.reward_date || reward.date),
      'ملاحظات': reward.notes || ''
    }));
    
    exportToExcel(data, 'المكافآت');
  }
  
  // تصدير الخصومات إلى Excel
  function exportDeductions() {
    const data = deductions.map(deduction => ({
      'الكود': deduction.employee_code,
      'اسم الموظف': deduction.employee_name,
      'الإدارة': deduction.department,
      'الخصم': deduction.amount,
      'سبب الخصم': deduction.reason,
      'تاريخ الخصم': formatDate(deduction.date),
      'ملاحظات': deduction.notes || ''
    }));
    
    exportToExcel(data, 'الخصومات');
  }
  
  // تصدير إلى Excel
  function exportToExcel(data, filename) {
    if (data.length === 0) {
      alert('لا توجد بيانات للتصدير');
      return;
    }
    
    // إنشاء CSV
    const headers = Object.keys(data[0]);
    const csvContent = [headers.join(',')];
    
    data.forEach(row => {
      const values = headers.map(header => {
        const value = row[header] || '';
        return `"${value.toString().replace(/"/g, '""')}"`;
      });
      csvContent.push(values.join(','));
    });
    
    // تحميل الملف
    const blob = new Blob(["\uFEFF" + csvContent.join('\n')], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
  
  // إعداد مستمعي الأحداث
  function setupEventListeners() {
    // أزرار حفظ النماذج
    if (saveRewardBtn) {
      saveRewardBtn.addEventListener('click', saveReward);
    }
    if (saveDeductionBtn) {
      saveDeductionBtn.addEventListener('click', saveDeduction);
    }
    
    // أزرار إعادة تعيين النماذج
    if (resetRewardFormBtn) {
      resetRewardFormBtn.addEventListener('click', resetRewardForm);
    }
    if (resetDeductionFormBtn) {
      resetDeductionFormBtn.addEventListener('click', resetDeductionForm);
    }
    
    // إعداد البحث المباشر للمكافآت
    setupRewardFilters();

    // إعداد البحث المباشر للخصومات
    setupDeductionFilters();

    // إعداد البحث عن الموظف في التقارير
    setupReportEmployeeSearch();
    
    // أزرار التصدير
    if (exportRewardsBtn) {
      exportRewardsBtn.addEventListener('click', exportRewards);
    }
    if (exportDeductionsBtn) {
      exportDeductionsBtn.addEventListener('click', exportDeductions);
    }
    
    // المودال
    if (saveEditBtn) {
      saveEditBtn.addEventListener('click', saveEdit);
    }
    if (closeModalBtn) {
      closeModalBtn.addEventListener('click', () => {
        editModal.style.display = 'none';
      });
    }
    if (cancelBtn) {
      cancelBtn.addEventListener('click', () => {
        editModal.style.display = 'none';
      });
    }
    
    // إغلاق المودال عند النقر خارجه
    window.addEventListener('click', (event) => {
      if (event.target === editModal) {
        editModal.style.display = 'none';
      }
      if (event.target === detailsModal) {
        closeDetailsModal();
      }
    });
    
    // تعيين التاريخ الحالي كافتراضي
    const today = new Date().toISOString().split('T')[0];
    if (rewardDate) rewardDate.value = today;
    if (deductionDate) deductionDate.value = today;

    // تعيين فترة افتراضية للتقارير (آخر 30 يوم)
    if (reportEndDate) reportEndDate.value = today;
    if (reportStartDate) {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      reportStartDate.value = thirtyDaysAgo.toISOString().split('T')[0];
    }

    // إعداد مستمعي أحداث التقارير
    setupReportsEventListeners();
  }

  // ===== وظائف التقارير =====

  // إعداد مستمعي أحداث التقارير
  function setupReportsEventListeners() {
    if (applyReportFiltersBtn) {
      applyReportFiltersBtn.addEventListener('click', applyReportFilters);
    }
    if (clearReportFiltersBtn) {
      clearReportFiltersBtn.addEventListener('click', clearReportFilters);
    }
  }

  // إعداد البحث عن الموظف في التقارير
  function setupReportEmployeeSearch() {
    if (!reportEmployeeSearch) return;

    const datalist = document.getElementById('reportEmployeeSuggestions');
    if (!datalist) return;

    reportEmployeeSearch.addEventListener('input', function() {
      const searchTerm = this.value.trim();

      if (!searchTerm || searchTerm === '') {
        datalist.innerHTML = '';
        return;
      }

      // البحث في الموظفين
      const searchTermLower = searchTerm.toLowerCase();
      const filteredEmployees = employees.filter(emp => {
        const fullName = emp.full_name ? String(emp.full_name).toLowerCase() : '';
        const code = emp.code ? String(emp.code).toLowerCase() : '';

        return fullName.includes(searchTermLower) ||
               code.includes(searchTermLower);
      });

      // تحديث الاقتراحات
      datalist.innerHTML = '';

      if (filteredEmployees.length > 0) {
        filteredEmployees.slice(0, 10).forEach(emp => {
          const option = document.createElement('option');
          // إذا كان البحث بالكود، اعرض الكود أولاً
          const isCodeSearch = /^\d+$/.test(searchTerm);
          if (isCodeSearch) {
            option.value = emp.code.toString();
          } else {
            option.value = emp.full_name;
          }
          option.setAttribute('data-code', emp.code);
          option.setAttribute('data-name', emp.full_name);
          datalist.appendChild(option);
        });
      }
    });

    // التعامل مع اختيار الموظف
    reportEmployeeSearch.addEventListener('change', function() {
      const selectedValue = this.value.trim();

      if (!selectedValue) return;

      // البحث عن الموظف بالاسم أو الكود
      const employee = employees.find(emp => {
        const nameMatch = emp.full_name.toLowerCase() === selectedValue.toLowerCase();
        const codeMatch = emp.code.toString() === selectedValue;
        return nameMatch || codeMatch;
      });

      if (employee) {
        // إذا تم البحث بالكود، اعرض الاسم في حقل البحث
        if (employee.code.toString() === selectedValue) {
          this.value = employee.full_name;
        }
        // تطبيق الفلاتر تلقائياً عند اختيار موظف
        applyReportFilters();
      }
    });
  }

  // دالة مساعدة لتحويل التاريخ إلى صيغة YYYY-MM-DD
  function formatDateForComparison(dateString) {
    if (!dateString) return '';

    try {
      console.log('تنسيق التاريخ:', dateString, 'نوع البيانات:', typeof dateString);

      // تحويل إلى نص أولاً
      let dateStr = String(dateString);

      // إذا كان التاريخ يحتوي على وقت، أخذ الجزء الأول فقط
      if (dateStr.includes(' ')) {
        dateStr = dateStr.split(' ')[0];
        console.log('تاريخ بعد إزالة الوقت:', dateStr);
      }

      // إذا كان التاريخ يحتوي على T (ISO format)
      if (dateStr.includes('T')) {
        dateStr = dateStr.split('T')[0];
        console.log('تاريخ بعد إزالة الوقت ISO:', dateStr);
      }

      // إذا كان التاريخ بالفعل في صيغة YYYY-MM-DD
      if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
        console.log('تاريخ في الصيغة الصحيحة:', dateStr);
        return dateStr;
      }

      // إذا كان التاريخ في صيغة YYYY/MM/DD
      if (/^\d{4}\/\d{1,2}\/\d{1,2}$/.test(dateStr)) {
        const parts = dateStr.split('/');
        const formatted = `${parts[0]}-${parts[1].padStart(2, '0')}-${parts[2].padStart(2, '0')}`;
        console.log('تاريخ محول من YYYY/MM/DD:', formatted);
        return formatted;
      }

      // إذا كان التاريخ في صيغة DD/MM/YYYY
      if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateStr)) {
        const parts = dateStr.split('/');
        const formatted = `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
        console.log('تاريخ محول من DD/MM/YYYY:', formatted);
        return formatted;
      }

      // إذا كان التاريخ في صيغة DD-MM-YYYY
      if (/^\d{1,2}-\d{1,2}-\d{4}$/.test(dateStr)) {
        const parts = dateStr.split('-');
        const formatted = `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
        console.log('تاريخ محول من DD-MM-YYYY:', formatted);
        return formatted;
      }

      // محاولة أخيرة: تحويل إلى Date object
      const date = new Date(dateStr);
      if (!isNaN(date.getTime())) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const formatted = `${year}-${month}-${day}`;
        console.log('تاريخ محول عبر Date object:', formatted);
        return formatted;
      }

      // إرجاع التاريخ كما هو كحل أخير
      console.log('تاريخ كما هو (لم يتم التعرف على التنسيق):', dateStr);
      return dateStr;
    } catch (error) {
      console.error('خطأ في تنسيق التاريخ:', error, 'التاريخ الأصلي:', dateString);
      return String(dateString);
    }
  }

  // تطبيق فلاتر التقارير
  function applyReportFilters() {
    const employeeSearch = reportEmployeeSearch?.value.trim() || '';
    const department = reportDepartmentFilter?.value || '';
    const startDate = reportStartDate?.value || '';
    const endDate = reportEndDate?.value || '';

    // تحويل endDate إلى نهاية اليوم إذا كان محدداً
    let endDateObj = null;
    if (endDate) {
      endDateObj = new Date(endDate);
      endDateObj.setHours(23, 59, 59, 999);
    }
    let startDateObj = null;
    if (startDate) {
      startDateObj = new Date(startDate);
      startDateObj.setHours(0, 0, 0, 0);
    }

    // فلترة المكافآت
    filteredRewards = rewards.filter(reward => {
      let matches = true;

      // فلترة بالموظف
      if (employeeSearch) {
        const employeeNameMatch = reward.employee_name &&
          reward.employee_name.toLowerCase().includes(employeeSearch.toLowerCase());
        const employeeCodeMatch = reward.employee_code &&
          reward.employee_code.toString().includes(employeeSearch);

        if (!employeeNameMatch && !employeeCodeMatch) {
          matches = false;
        }
      }

      if (department && reward.department !== department) {
        matches = false;
      }
      // مقارنة التاريخ
      if (startDate && reward.reward_date) {
        const rewardDateObj = new Date(reward.reward_date);
        if (rewardDateObj < startDateObj) {
          matches = false;
        }
      }
      if (endDate && reward.reward_date) {
        const rewardDateObj = new Date(reward.reward_date);
        if (rewardDateObj > endDateObj) {
          matches = false;
        }
      }
      return matches;
    });

    // فلترة الخصومات
    filteredDeductions = deductions.filter(deduction => {
      let matches = true;

      // فلترة بالموظف
      if (employeeSearch) {
        const employeeNameMatch = deduction.employee_name &&
          deduction.employee_name.toLowerCase().includes(employeeSearch.toLowerCase());
        const employeeCodeMatch = deduction.employee_code &&
          deduction.employee_code.toString().includes(employeeSearch);

        if (!employeeNameMatch && !employeeCodeMatch) {
          matches = false;
        }
      }

      if (department && deduction.department !== department) {
        matches = false;
      }
      if (startDate && deduction.date) {
        const deductionDateObj = new Date(deduction.date);
        if (deductionDateObj < startDateObj) {
          matches = false;
        }
      }
      if (endDate && deduction.date) {
        const deductionDateObj = new Date(deduction.date);
        if (deductionDateObj > endDateObj) {
          matches = false;
        }
      }
      return matches;
    });

    updateReportsDisplay();
  }

  // مسح فلاتر التقارير
  function clearReportFilters() {
    if (reportEmployeeSearch) reportEmployeeSearch.value = '';
    if (reportDepartmentFilter) reportDepartmentFilter.value = '';
    if (reportStartDate) reportStartDate.value = '';
    if (reportEndDate) reportEndDate.value = '';

    // إعادة تعيين التواريخ الافتراضية
    setDefaultDateRange();

    filteredRewards = [...rewards];
    filteredDeductions = [...deductions];

    updateReportsDisplay();
  }

  // تحديث عرض التقارير
  function updateReportsDisplay() {
    updateStatsCards();
    updateTopRewardsTable();
    updateTopDeductionsTable();
    populateReportDepartmentFilter();
  }

  // تحديث البطاقات الإحصائية
  function updateStatsCards() {
    const totalRewards = filteredRewards.reduce((sum, reward) => {
      const amount = parseFloat(reward.amount) || 0;
      return sum + amount;
    }, 0);

    const totalDeductions = filteredDeductions.reduce((sum, deduction) => {
      const amount = parseFloat(deduction.amount) || 0;
      return sum + amount;
    }, 0);

    const netAmount = totalRewards - totalDeductions;
    const totalRewardsAndDeductions = totalRewards + totalDeductions;
    const totalOperations = filteredRewards.length + filteredDeductions.length;

    if (totalRewardsEl) {
      totalRewardsEl.textContent = formatCurrency(totalRewards);
    }
    if (totalDeductionsEl) {
      totalDeductionsEl.textContent = formatCurrency(totalDeductions);
    }
    if (totalRewardsAndDeductionsEl) {
      totalRewardsAndDeductionsEl.textContent = formatCurrency(totalRewardsAndDeductions);
    }
    if (totalCountEl) {
      totalCountEl.textContent = `${totalOperations} عملية`;
    }
    if (netAmountEl) {
      netAmountEl.textContent = formatCurrency(netAmount);
    }
    if (rewardsCountEl) {
      rewardsCountEl.textContent = `${filteredRewards.length} مكافأة`;
    }
    if (deductionsCountEl) {
      deductionsCountEl.textContent = `${filteredDeductions.length} خصم`;
    }

    // تحديث ألوان البطاقة الصافية
    const netCard = document.querySelector('.net-card');
    if (netCard) {
      netCard.classList.remove('positive', 'negative', 'zero');
      if (netAmount > 0) {
        netCard.classList.add('positive');
      } else if (netAmount < 0) {
        netCard.classList.add('negative');
      } else {
        netCard.classList.add('zero');
      }
    }
  }

  // تنسيق العملة
  function formatCurrency(amount) {
    if (isNaN(amount) || amount === null || amount === undefined) {
      return '0 جنيه';
    }
    return new Intl.NumberFormat('ar-EG', {
      style: 'decimal',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount) + ' جنيه';
  }

  // تحديث جدول أعلى المكافآت
  function updateTopRewardsTable() {
    if (!topRewardsTableBody) return;

    // تجميع المكافآت حسب الموظف
    const employeeRewards = {};
    filteredRewards.forEach(reward => {
      const key = reward.employee_code;
      if (!employeeRewards[key]) {
        employeeRewards[key] = {
          employee_code: reward.employee_code,
          employee_name: reward.employee_name,
          department: reward.department,
          count: 0,
          total: 0
        };
      }
      employeeRewards[key].count++;
      employeeRewards[key].total += parseFloat(reward.amount) || 0;
    });

    // تحويل إلى مصفوفة وترتيب
    const sortedRewards = Object.values(employeeRewards)
      .sort((a, b) => b.total - a.total)
      .slice(0, 10); // أعلى 10 موظفين

    topRewardsTableBody.innerHTML = '';

    if (sortedRewards.length === 0) {
      topRewardsTableBody.innerHTML = '<tr><td colspan="6">لا توجد بيانات</td></tr>';
      return;
    }

    sortedRewards.forEach((employee, index) => {
      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${index + 1}</td>
        <td>${employee.employee_code}</td>
        <td>${employee.employee_name}</td>
        <td>${employee.department}</td>
        <td>${employee.count}</td>
        <td class="amount-cell reward-amount">${formatCurrency(employee.total)}</td>
      `;
      topRewardsTableBody.appendChild(row);
    });
  }

  // تحديث جدول أعلى الخصومات
  function updateTopDeductionsTable() {
    if (!topDeductionsTableBody) return;

    // تجميع الخصومات حسب الموظف
    const employeeDeductions = {};
    filteredDeductions.forEach(deduction => {
      const key = deduction.employee_code;
      if (!employeeDeductions[key]) {
        employeeDeductions[key] = {
          employee_code: deduction.employee_code,
          employee_name: deduction.employee_name,
          department: deduction.department,
          count: 0,
          total: 0
        };
      }
      employeeDeductions[key].count++;
      employeeDeductions[key].total += parseFloat(deduction.amount) || 0;
    });

    // تحويل إلى مصفوفة وترتيب
    const sortedDeductions = Object.values(employeeDeductions)
      .sort((a, b) => b.total - a.total)
      .slice(0, 10); // أعلى 10 موظفين

    topDeductionsTableBody.innerHTML = '';

    if (sortedDeductions.length === 0) {
      topDeductionsTableBody.innerHTML = '<tr><td colspan="6">لا توجد بيانات</td></tr>';
      return;
    }

    sortedDeductions.forEach((employee, index) => {
      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${index + 1}</td>
        <td>${employee.employee_code}</td>
        <td>${employee.employee_name}</td>
        <td>${employee.department}</td>
        <td>${employee.count}</td>
        <td class="amount-cell deduction-amount">${formatCurrency(employee.total)}</td>
      `;
      topDeductionsTableBody.appendChild(row);
    });
  }

  // ملء فلتر الإدارات للتقارير
  function populateReportDepartmentFilter() {
    if (!reportDepartmentFilter) return;

    const currentValue = reportDepartmentFilter.value;
    reportDepartmentFilter.innerHTML = '<option value="">كل الإدارات</option>';

    departments.forEach(dept => {
      const option = document.createElement('option');
      option.value = dept;
      option.textContent = dept;
      if (dept === currentValue) {
        option.selected = true;
      }
      reportDepartmentFilter.appendChild(option);
    });
  }

  // تبديل عرض الجداول
  window.toggleTable = function(tableId) {
    const table = document.getElementById(tableId);
    const toggleIcon = document.getElementById(tableId.replace('Table', 'Toggle'));

    if (table && toggleIcon) {
      if (table.style.display === 'none') {
        table.style.display = 'block';
        toggleIcon.classList.add('rotated');
      } else {
        table.style.display = 'none';
        toggleIcon.classList.remove('rotated');
      }
    }
  };

  // عرض تفاصيل المكافآت
  window.showRewardsDetails = function() {
    currentDetailsData = filteredRewards.map(reward => ({
      ...reward,
      type: 'مكافأة',
      date: reward.reward_date || reward.date
    }));
    currentDetailsType = 'rewards';
    showDetailsModal('تفاصيل المكافآت');
  };

  // عرض تفاصيل الخصومات
  window.showDeductionsDetails = function() {
    currentDetailsData = filteredDeductions.map(deduction => ({
      ...deduction,
      type: 'خصم'
    }));
    currentDetailsType = 'deductions';
    showDetailsModal('تفاصيل الخصومات');
  };

  // عرض تفاصيل الصافي
  // عرض تفاصيل جميع المكافآت والخصومات
  window.showAllDetails = function() {
    const rewardsData = filteredRewards.map(reward => ({
      ...reward,
      type: 'مكافأة',
      date: reward.reward_date || reward.date
    }));
    const deductionsData = filteredDeductions.map(deduction => ({
      ...deduction,
      type: 'خصم'
    }));

    currentDetailsData = [...rewardsData, ...deductionsData]
      .sort((a, b) => new Date(b.date) - new Date(a.date));
    currentDetailsType = 'all';
    showDetailsModal('تفاصيل جميع المكافآت والخصومات');
  };

  window.showNetDetails = function() {
    const rewardsData = filteredRewards.map(reward => ({
      ...reward,
      type: 'مكافأة',
      date: reward.reward_date || reward.date
    }));
    const deductionsData = filteredDeductions.map(deduction => ({
      ...deduction,
      type: 'خصم'
    }));

    currentDetailsData = [...rewardsData, ...deductionsData]
      .sort((a, b) => new Date(b.date) - new Date(a.date));
    currentDetailsType = 'net';
    showDetailsModal('تفاصيل الصافي (المكافآت والخصومات)');
  };

  // عرض modal التفاصيل
  function showDetailsModal(title) {
    if (!detailsModal || !detailsModalTitle || !detailsTableBody) return;

    detailsModalTitle.textContent = title;

    // تحديث الملخص
    const totalCount = currentDetailsData.length;
    const totalAmount = currentDetailsData.reduce((sum, item) => {
      if (item.type === 'مكافأة') {
        return sum + (parseFloat(item.amount) || 0);
      } else {
        return sum - (parseFloat(item.amount) || 0);
      }
    }, 0);

    if (detailsTotalCount) detailsTotalCount.textContent = totalCount;
    if (detailsTotalAmount) detailsTotalAmount.textContent = formatCurrency(Math.abs(totalAmount));

    // ملء الجدول
    detailsTableBody.innerHTML = '';

    if (currentDetailsData.length === 0) {
      detailsTableBody.innerHTML = '<tr><td colspan="8">لا توجد بيانات</td></tr>';
    } else {
      currentDetailsData.forEach(item => {
        const row = document.createElement('tr');
        const amountClass = item.type === 'مكافأة' ? 'reward-amount' : 'deduction-amount';
        row.innerHTML = `
          <td>${item.employee_code}</td>
          <td>${item.employee_name}</td>
          <td>${item.department}</td>
          <td>${item.type}</td>
          <td class="amount-cell ${amountClass}">${formatCurrency(item.amount || 0)}</td>
          <td>${item.reason}</td>
          <td>${formatDate(item.date)}</td>
          <td>${item.notes || '-'}</td>
        `;

        // إضافة اللون الأحمر للمكافآت/خصومات قبل 26-6 (العام المالي السابق)
        if (isBeforeJune26(item.date)) {
          row.style.backgroundColor = '#ffebee'; // خلفية حمراء فاتحة
          row.style.color = '#c62828'; // نص أحمر داكن
          row.style.fontWeight = 'bold';
          row.title = `${item.type} من العام المالي السابق (قبل 26-6)`;
        }

        detailsTableBody.appendChild(row);
      });
    }

    detailsModal.style.display = 'block';
  }

  // إغلاق modal التفاصيل
  window.closeDetailsModal = function() {
    if (detailsModal) {
      detailsModal.style.display = 'none';
    }
  };

  // تصدير أعلى المكافآت
  window.exportTopRewards = function() {
    const employeeRewards = {};
    filteredRewards.forEach(reward => {
      const key = reward.employee_code;
      if (!employeeRewards[key]) {
        employeeRewards[key] = {
          employee_code: reward.employee_code,
          employee_name: reward.employee_name,
          department: reward.department,
          count: 0,
          total: 0
        };
      }
      employeeRewards[key].count++;
      employeeRewards[key].total += parseFloat(reward.amount) || 0;
    });

    const sortedRewards = Object.values(employeeRewards)
      .sort((a, b) => b.total - a.total);

    const data = sortedRewards.map((employee, index) => ({
      'الترتيب': index + 1,
      'الكود': employee.employee_code,
      'اسم الموظف': employee.employee_name,
      'الإدارة': employee.department,
      'عدد المكافآت': employee.count,
      'إجمالي المكافآت': formatCurrency(employee.total)
    }));

    exportToExcel(data, 'أعلى_المكافآت');
  };

  // تصدير أعلى الخصومات
  window.exportTopDeductions = function() {
    const employeeDeductions = {};
    filteredDeductions.forEach(deduction => {
      const key = deduction.employee_code;
      if (!employeeDeductions[key]) {
        employeeDeductions[key] = {
          employee_code: deduction.employee_code,
          employee_name: deduction.employee_name,
          department: deduction.department,
          count: 0,
          total: 0
        };
      }
      employeeDeductions[key].count++;
      employeeDeductions[key].total += parseFloat(deduction.amount) || 0;
    });

    const sortedDeductions = Object.values(employeeDeductions)
      .sort((a, b) => b.total - a.total);

    const data = sortedDeductions.map((employee, index) => ({
      'الترتيب': index + 1,
      'الكود': employee.employee_code,
      'اسم الموظف': employee.employee_name,
      'الإدارة': employee.department,
      'عدد الخصومات': employee.count,
      'إجمالي الخصومات': formatCurrency(employee.total)
    }));

    exportToExcel(data, 'أعلى_الخصومات');
  };

  // تصدير بيانات التفاصيل
  window.exportDetailsData = function() {
    if (currentDetailsData.length === 0) {
      alert('لا توجد بيانات للتصدير');
      return;
    }

    const data = currentDetailsData.map(item => ({
      'الكود': item.employee_code,
      'اسم الموظف': item.employee_name,
      'الإدارة': item.department,
      'النوع': item.type,
      'المبلغ': formatCurrency(item.amount || 0),
      'السبب': item.reason,
      'التاريخ': formatDate(item.date),
      'ملاحظات': item.notes || ''
    }));

    let filename = 'تفاصيل_التقرير';
    if (currentDetailsType === 'rewards') {
      filename = 'تفاصيل_المكافآت';
    } else if (currentDetailsType === 'deductions') {
      filename = 'تفاصيل_الخصومات';
    } else if (currentDetailsType === 'net') {
      filename = 'تفاصيل_الصافي';
    }

    exportToExcel(data, filename);
  };

  // تحديث البيانات عند تحميل التقارير
  function updateReportsData() {
    // تطبيق الفلاتر بدلاً من عرض جميع البيانات
    applyReportFilters();
  }

  // إضافة رسالة عدم وجود بيانات
  function showNoDataMessage(container, message = 'لا توجد بيانات') {
    if (container) {
      container.innerHTML = `<tr><td colspan="100%" style="text-align: center; padding: 20px; color: #666;">${message}</td></tr>`;
    }
  }

  // التهيئة
  async function init() {
    setupTabs();
    setupEventListeners();
    await loadData();

    // تعيين التواريخ الافتراضية بعد تحميل البيانات
    setDefaultDateRange();

    // تطبيق الفلاتر تلقائياً لعرض البيانات
    applyReportFilters();

    // تطبيق الصلاحيات بعد تحميل البيانات
    setTimeout(() => {
      applyButtonPermissions();
    }, 50);
  }

  // التحقق من التبويب المحدد من البطاقات
  // التحقق من المحتوى المحدد من البطاقات
  function checkSelectedContent() {
    const selectedContent = localStorage.getItem('selectedRewardsDeductionsTab');

    if (selectedContent) {
      // حذف المحتوى المحفوظ
      localStorage.removeItem('selectedRewardsDeductionsTab');

      // عرض المحتوى المناسب
      showContent(selectedContent);
    } else {
      // عرض المحتوى الافتراضي (المكافآت)
      showContent('add-reward');
    }
  }

  // عرض المحتوى المحدد
  function showContent(contentType) {
    console.log('عرض المحتوى:', contentType);

    // التحقق من الصلاحيات قبل عرض المحتوى
    const permissions = JSON.parse(localStorage.getItem('permissions') || '{}');

    if (contentType === 'reports') {
      if (!permissions.view_rewards_deductions_reports) {
        alert('ليس لديك صلاحية لعرض التقارير');
        return;
      }
    } else if (contentType === 'add-reward') {
      if (!permissions.add_reward) {
        alert('ليس لديك صلاحية لإضافة المكافآت');
        return;
      }
    } else if (contentType === 'add-deduction') {
      if (!permissions.add_deduction) {
        alert('ليس لديك صلاحية لإضافة الخصومات');
        return;
      }
    }

    // إخفاء جميع المحتويات
    const allContents = document.querySelectorAll('.tab-content');
    allContents.forEach(content => {
      content.classList.remove('active');
      content.style.display = 'none';
    });

    // عرض المحتوى المحدد
    const targetContent = document.getElementById(contentType);
    if (targetContent) {
      targetContent.classList.add('active');
      targetContent.style.display = 'block';

      // تحديث عنوان الصفحة
      const pageTitle = document.querySelector('h1');
      if (pageTitle) {
        if (contentType === 'add-reward') {
          pageTitle.textContent = 'المكافآت';
        } else if (contentType === 'add-deduction') {
          pageTitle.textContent = 'الخصومات';
        } else if (contentType === 'reports') {
          pageTitle.textContent = 'التقارير';
        }
      }

      // تحديث البيانات حسب نوع المحتوى
      if (contentType === 'reports') {
        loadRewards();
      }
    } else {
      console.error('لم يتم العثور على المحتوى:', contentType);
    }
  }

  // إعداد البحث المباشر للمكافآت
  function setupRewardFilters() {
    const filterInputs = ['filterRewardEmployeeCode', 'filterRewardEmployeeName', 'filterRewardFromDate', 'filterRewardToDate'];

    filterInputs.forEach(inputId => {
      const input = document.getElementById(inputId);
      if (input) {
        input.addEventListener('input', function() {
          clearTimeout(this.filterTimeout);
          this.filterTimeout = setTimeout(() => {
            applyRewardFilters();
          }, 300);
        });
      }
    });

    // زر مسح الفلاتر
    const clearBtn = document.getElementById('clearRewardFiltersBtn');
    if (clearBtn) {
      clearBtn.addEventListener('click', clearRewardFilters);
    }
  }

  // إعداد البحث المباشر للخصومات
  function setupDeductionFilters() {
    const filterInputs = ['filterDeductionEmployeeCode', 'filterDeductionEmployeeName', 'filterDeductionFromDate', 'filterDeductionToDate'];

    filterInputs.forEach(inputId => {
      const input = document.getElementById(inputId);
      if (input) {
        input.addEventListener('input', function() {
          clearTimeout(this.filterTimeout);
          this.filterTimeout = setTimeout(() => {
            applyDeductionFilters();
          }, 300);
        });
      }
    });

    // زر مسح الفلاتر
    const clearBtn = document.getElementById('clearDeductionFiltersBtn');
    if (clearBtn) {
      clearBtn.addEventListener('click', clearDeductionFilters);
    }
  }

  // تطبيق فلاتر المكافآت
  function applyRewardFilters() {
    const employeeCode = document.getElementById('filterRewardEmployeeCode')?.value.trim() || '';
    const employeeName = document.getElementById('filterRewardEmployeeName')?.value.trim() || '';
    const fromDate = document.getElementById('filterRewardFromDate')?.value || '';
    const toDate = document.getElementById('filterRewardToDate')?.value || '';

    const filteredRewards = rewards.filter(reward => {
      let matches = true;

      // فلترة بالكود
      if (employeeCode && !reward.employee_code.toString().includes(employeeCode)) {
        matches = false;
      }

      // فلترة بالاسم
      if (employeeName && !reward.employee_name.toLowerCase().includes(employeeName.toLowerCase())) {
        matches = false;
      }

      // فلترة بالتاريخ من
      if (fromDate && reward.reward_date) {
        const rewardDate = new Date(reward.reward_date);
        const filterFromDate = new Date(fromDate);
        if (rewardDate < filterFromDate) {
          matches = false;
        }
      }

      // فلترة بالتاريخ إلى
      if (toDate && reward.reward_date) {
        const rewardDate = new Date(reward.reward_date);
        const filterToDate = new Date(toDate);
        if (rewardDate > filterToDate) {
          matches = false;
        }
      }

      return matches;
    });

    displayRewards(filteredRewards);
  }

  // تطبيق فلاتر الخصومات
  function applyDeductionFilters() {
    const employeeCode = document.getElementById('filterDeductionEmployeeCode')?.value.trim() || '';
    const employeeName = document.getElementById('filterDeductionEmployeeName')?.value.trim() || '';
    const fromDate = document.getElementById('filterDeductionFromDate')?.value || '';
    const toDate = document.getElementById('filterDeductionToDate')?.value || '';

    const filteredDeductions = deductions.filter(deduction => {
      let matches = true;

      // فلترة بالكود
      if (employeeCode && !deduction.employee_code.toString().includes(employeeCode)) {
        matches = false;
      }

      // فلترة بالاسم
      if (employeeName && !deduction.employee_name.toLowerCase().includes(employeeName.toLowerCase())) {
        matches = false;
      }

      // فلترة بالتاريخ من
      if (fromDate && deduction.date) {
        const deductionDate = new Date(deduction.date);
        const filterFromDate = new Date(fromDate);
        if (deductionDate < filterFromDate) {
          matches = false;
        }
      }

      // فلترة بالتاريخ إلى
      if (toDate && deduction.date) {
        const deductionDate = new Date(deduction.date);
        const filterToDate = new Date(toDate);
        if (deductionDate > filterToDate) {
          matches = false;
        }
      }

      return matches;
    });

    displayDeductions(filteredDeductions);
  }

  // مسح فلاتر المكافآت
  function clearRewardFilters() {
    document.getElementById('filterRewardEmployeeCode').value = '';
    document.getElementById('filterRewardEmployeeName').value = '';
    document.getElementById('filterRewardFromDate').value = '';
    document.getElementById('filterRewardToDate').value = '';
    displayRewards(rewards);
  }

  // مسح فلاتر الخصومات
  function clearDeductionFilters() {
    document.getElementById('filterDeductionEmployeeCode').value = '';
    document.getElementById('filterDeductionEmployeeName').value = '';
    document.getElementById('filterDeductionFromDate').value = '';
    document.getElementById('filterDeductionToDate').value = '';
    displayDeductions(deductions);
  }

  // دوال التحكم في الصفحات للمكافآت
  function updatePaginationControlsRewards() {
    const totalPages = Math.ceil(totalRewards / itemsPerPageRewards);
    const paginationContainer = document.getElementById('paginationContainerRewards');

    if (!paginationContainer) return;

    // إظهار التحكم في الصفحات دائماً (حتى لو كانت صفحة واحدة)
    // إخفاء فقط إذا لم توجد بيانات على الإطلاق
    if (totalRewards === 0) {
      paginationContainer.style.display = 'none';
      return;
    }

    paginationContainer.style.display = 'flex';

    // تحديث أزرار السابق والتالي
    const prevBtn = document.getElementById('prevBtnRewards');
    const nextBtn = document.getElementById('nextBtnRewards');

    if (prevBtn) {
      prevBtn.disabled = currentPageRewards <= 1;
      prevBtn.onclick = () => changePageRewards(-1);
    }

    if (nextBtn) {
      nextBtn.disabled = currentPageRewards >= totalPages;
      nextBtn.onclick = () => changePageRewards(1);
    }

    // تحديث أرقام الصفحات
    updatePageNumbersRewards(totalPages);

    // تحديث معلومات الصفحة
    const pageInfo = document.getElementById('pageInfoRewards');
    if (pageInfo) {
      const startItem = (currentPageRewards - 1) * itemsPerPageRewards + 1;
      const endItem = Math.min(currentPageRewards * itemsPerPageRewards, totalRewards);
      pageInfo.textContent = `عرض ${startItem} - ${endItem} من أصل ${totalRewards} مكافأة`;
    }
  }

  function updatePageNumbersRewards(totalPages) {
    const pageNumbersContainer = document.getElementById('pageNumbersRewards');
    if (!pageNumbersContainer) return;

    pageNumbersContainer.innerHTML = '';

    // إظهار رقم الصفحة حتى لو كانت صفحة واحدة فقط
    if (totalPages === 0) return;

    // حساب نطاق الصفحات المعروضة
    let startPage = Math.max(1, currentPageRewards - 2);
    let endPage = Math.min(totalPages, currentPageRewards + 2);

    // التأكد من عرض 5 صفحات على الأقل إذا كانت متوفرة
    if (endPage - startPage < 4) {
      if (startPage === 1) {
        endPage = Math.min(totalPages, startPage + 4);
      } else if (endPage === totalPages) {
        startPage = Math.max(1, endPage - 4);
      }
    }

    // إضافة أزرار أرقام الصفحات
    for (let i = startPage; i <= endPage; i++) {
      addPageButtonRewards(i);
    }
  }

  function addPageButtonRewards(pageNumber) {
    const pageNumbersContainer = document.getElementById('pageNumbersRewards');
    const button = document.createElement('button');
    button.textContent = pageNumber;
    button.className = 'pagination-btn page-btn';
    if (pageNumber === currentPageRewards) {
      button.classList.add('active');
    }
    button.addEventListener('click', () => goToPageRewards(pageNumber));
    pageNumbersContainer.appendChild(button);
  }

  function goToPageRewards(page) {
    const totalPages = Math.ceil(totalRewards / itemsPerPageRewards);
    if (page >= 1 && page <= totalPages) {
      currentPageRewards = page;
      displayRewards(filteredRewardsData);
    }
  }

  function changePageRewards(direction) {
    const newPage = currentPageRewards + direction;
    goToPageRewards(newPage);
  }

  // دوال التحكم في الصفحات للخصومات
  function updatePaginationControlsDeductions() {
    const totalPages = Math.ceil(totalDeductions / itemsPerPageDeductions);
    const paginationContainer = document.getElementById('paginationContainerDeductions');

    if (!paginationContainer) return;

    // إظهار التحكم في الصفحات دائماً (حتى لو كانت صفحة واحدة)
    // إخفاء فقط إذا لم توجد بيانات على الإطلاق
    if (totalDeductions === 0) {
      paginationContainer.style.display = 'none';
      return;
    }

    paginationContainer.style.display = 'flex';

    // تحديث أزرار السابق والتالي
    const prevBtn = document.getElementById('prevBtnDeductions');
    const nextBtn = document.getElementById('nextBtnDeductions');

    if (prevBtn) {
      prevBtn.disabled = currentPageDeductions <= 1;
      prevBtn.onclick = () => changePageDeductions(-1);
    }

    if (nextBtn) {
      nextBtn.disabled = currentPageDeductions >= totalPages;
      nextBtn.onclick = () => changePageDeductions(1);
    }

    // تحديث أرقام الصفحات
    updatePageNumbersDeductions(totalPages);

    // تحديث معلومات الصفحة
    const pageInfo = document.getElementById('pageInfoDeductions');
    if (pageInfo) {
      const startItem = (currentPageDeductions - 1) * itemsPerPageDeductions + 1;
      const endItem = Math.min(currentPageDeductions * itemsPerPageDeductions, totalDeductions);
      pageInfo.textContent = `عرض ${startItem} - ${endItem} من أصل ${totalDeductions} خصم`;
    }
  }

  function updatePageNumbersDeductions(totalPages) {
    const pageNumbersContainer = document.getElementById('pageNumbersDeductions');
    if (!pageNumbersContainer) return;

    pageNumbersContainer.innerHTML = '';

    // إظهار رقم الصفحة حتى لو كانت صفحة واحدة فقط
    if (totalPages === 0) return;

    // حساب نطاق الصفحات المعروضة
    let startPage = Math.max(1, currentPageDeductions - 2);
    let endPage = Math.min(totalPages, currentPageDeductions + 2);

    // التأكد من عرض 5 صفحات على الأقل إذا كانت متوفرة
    if (endPage - startPage < 4) {
      if (startPage === 1) {
        endPage = Math.min(totalPages, startPage + 4);
      } else if (endPage === totalPages) {
        startPage = Math.max(1, endPage - 4);
      }
    }

    // إضافة أزرار أرقام الصفحات
    for (let i = startPage; i <= endPage; i++) {
      addPageButtonDeductions(i);
    }
  }

  function addPageButtonDeductions(pageNumber) {
    const pageNumbersContainer = document.getElementById('pageNumbersDeductions');
    const button = document.createElement('button');
    button.textContent = pageNumber;
    button.className = 'pagination-btn page-btn';
    if (pageNumber === currentPageDeductions) {
      button.classList.add('active');
    }
    button.addEventListener('click', () => goToPageDeductions(pageNumber));
    pageNumbersContainer.appendChild(button);
  }

  function goToPageDeductions(page) {
    const totalPages = Math.ceil(totalDeductions / itemsPerPageDeductions);
    if (page >= 1 && page <= totalPages) {
      currentPageDeductions = page;
      displayDeductions(filteredDeductionsData);
    }
  }

  function changePageDeductions(direction) {
    const newPage = currentPageDeductions + direction;
    goToPageDeductions(newPage);
  }

  // بدء التطبيق
  init();

  // التحقق من المحتوى المحدد
  checkSelectedContent();
});