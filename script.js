document.addEventListener("DOMContentLoaded", () => {
  // تهيئة باقي الصفحة
  const tableBody = document.getElementById("employeeTableBody");

  // استخدام المتغيرات من config.js
  let API_URL = window.CONFIG ? window.CONFIG.API_URL : (localStorage.getItem('serverUrl') || "http://localhost:5500/api");
  let allEmployees = window.GlobalState ? window.GlobalState.allEmployees : []; // لتخزين جميع الموظفين
  let filteredEmployees = window.GlobalState ? window.GlobalState.filteredEmployees : []; // إضافة متغير للنتائج المفلترة
  let serverRunning = window.GlobalState ? window.GlobalState.serverRunning : false;

  // متغيرات نظام الصفحات
  let currentPage = 1;
  const itemsPerPage = 50;
  let totalEmployees = 0;

  // إضافة نافذة منبثقة لتغيير السيرفر
  const modal = document.createElement('div');
  modal.className = 'modal';
  modal.innerHTML = `
    <div class="modal-content">
      <span class="close">&times;</span>
      <h2>تغيير عنوان السيرفر</h2>
      <form class="server-form">
        <input type="text" id="serverUrl" placeholder="أدخل عنوان السيرفر (مثال: http://localhost:5500/api)" required>
        <button type="submit">حفظ</button>
      </form>
    </div>
  `;
  document.body.appendChild(modal);

  // استخدام دالة تحديث حالة السيرفر من shared-utils.js
  function updateServerStatus(connected) {
    if (window.SharedUtils && window.SharedUtils.updateServerStatus) {
      window.SharedUtils.updateServerStatus(connected);
      serverRunning = connected;
    } else {
      // fallback للكود القديم
      const statusBtn = document.getElementById('serverStatus');
      const toggleBtn = document.getElementById('toggleServer');

      if (connected) {
        statusBtn.textContent = 'حالة السيرفر: متصل';
        statusBtn.className = 'status-btn connected';
        toggleBtn.textContent = 'إيقاف السيرفر';
        serverRunning = true;
        toggleBtn.disabled = false;
      } else {
        statusBtn.textContent = 'حالة السيرفر: غير متصل';
        statusBtn.className = 'status-btn disconnected';
        toggleBtn.textContent = 'تشغيل السيرفر';
        serverRunning = false;
        toggleBtn.disabled = false;
      }
    }
  }

  // التحقق من حالة السيرفر
  async function checkServerStatus() {
    try {
      const response = await fetch(`${API_URL}/status`);
      if (response.ok) {
        updateServerStatus(true);
        loadEmployees();
        return true;
      }
    } catch (error) {
      console.error('Server check failed:', error);
      updateServerStatus(false);
      return false;
    }
    return false;
  }

  // تشغيل/إيقاف السيرفر
  async function toggleServer() {
    const toggleBtn = document.getElementById('toggleServer');
    toggleBtn.disabled = true;

    try {
      if (!serverRunning) {
        // محاولة الاتصال بالسيرفر
        const response = await fetch(`${API_URL}/status`);
        if (response.ok) {
          updateServerStatus(true);
          await loadEmployees();
        } else {
          throw new Error('فشل الاتصال بالسيرفر');
        }
      } else {
        // إيقاف الاتصال بالسيرفر
        updateServerStatus(false);
        tableBody.innerHTML = '';
        allEmployees = [];
      }
    } catch (error) {
      console.error('Server operation failed:', error);
      alert(serverRunning ? 'فشل في إيقاف السيرفر' : 'فشل في تشغيل السيرفر');
      updateServerStatus(serverRunning);
    } finally {
      toggleBtn.disabled = false;
    }
  }



  // إغلاق النافذة المنبثقة
  document.querySelector('.close').onclick = function() {
    modal.style.display = 'none';
  }

  // حفظ عنوان السيرفر الجديد
  document.querySelector('.server-form').onsubmit = async function(e) {
    e.preventDefault();
    const newUrl = document.getElementById('serverUrl').value.trim();
    if (newUrl) {
      const oldUrl = API_URL;
      API_URL = newUrl;
      localStorage.setItem('serverUrl', newUrl);
      modal.style.display = 'none';

      const isConnected = await checkServerStatus();
      if (!isConnected) {
        API_URL = oldUrl;
        localStorage.setItem('serverUrl', oldUrl);
        alert('فشل الاتصال بالسيرفر الجديد. تم استعادة العنوان السابق.');
      }
    }
  }

  // إضافة مستمعي الأحداث
  document.getElementById('toggleServer').addEventListener('click', toggleServer);


  // التحقق من تسجيل الدخول والصلاحيات
  function checkAuth() {
    const token = localStorage.getItem('token');
    if (!token) {
      window.location.href = 'login.html';
      return false;
    }
    return true;
  }

  // التحقق من صلاحيات المستخدم
  function hasPermission(permission) {
    const permissionsStr = localStorage.getItem('permissions');
    if (!permissionsStr) return false;
    
    try {
      const permissions = JSON.parse(permissionsStr);
      return permissions[permission] === true;
    } catch (error) {
      console.error('خطأ في قراءة الصلاحيات:', error);
      return false;
    }
  }

  // تطبيق الصلاحيات على واجهة المستخدم
  function applyPermissions() {
    // استخدام نظام الصلاحيات الجديد
    if (window.permissionManager) {
      window.permissionManager.applyPermissions();
      return;
    }
    
    // النظام القديم كاحتياطي
    // إزالة أزرار الإضافة والتعديل والحذف إذا لم يكن لدى المستخدم الصلاحيات المناسبة
    if (!hasPermission('can_add')) {
      const addElements = document.querySelectorAll('a[href="add.html"], .add-btn, [data-permission="can_add"]');
      addElements.forEach(element => {
        element.remove();
      });
    }
    
    if (!hasPermission('can_edit')) {
      const editElements = document.querySelectorAll('.edit-btn, [data-permission="can_edit"]');
      editElements.forEach(element => {
        element.remove();
      });
    }
    
    if (!hasPermission('can_delete')) {
      const deleteElements = document.querySelectorAll('.delete-btn, [data-permission="can_delete"], #deleteAllBtn');
      deleteElements.forEach(element => {
        element.remove();
      });
    }
    

  }

  // التحقق من حالة السيرفر عند تحميل الصفحة
  checkServerStatus();
  
  // التحقق من تسجيل الدخول وتطبيق الصلاحيات
  if (checkAuth()) {
    // استرجاع الصلاحيات من الخادم
    async function fetchUserPermissions() {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/me`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          if (data.permissions) {
            localStorage.setItem('permissions', JSON.stringify(data.permissions));
          }
          // حفظ اسم المستخدم للعرض في الواجهة
          if (data.username) {
            localStorage.setItem('username', data.username);
            localStorage.setItem('userName', data.username);
          }
          applyPermissions();
        } else if (response.status === 401 || response.status === 403) {
          // إذا كان التوكن غير صالح، إعادة توجيه المستخدم إلى صفحة تسجيل الدخول
          localStorage.removeItem('token');
          localStorage.removeItem('username');
          localStorage.removeItem('permissions');
          window.location.href = 'login.html';
        }
      } catch (error) {
        console.error('خطأ في جلب صلاحيات المستخدم:', error);
      }
    }
    
    fetchUserPermissions();
  }

  // استخدام دالة معالجة الأخطاء من shared-utils.js
  const handleApiError = (error, message) => {
    if (window.SharedUtils && window.SharedUtils.handleApiError) {
      window.SharedUtils.handleApiError(error, message);
    } else {
      // fallback للكود القديم
      console.error('API Error:', error);
      console.error('Error details:', {
        message: error.message,
        status: error.status,
        statusText: error.statusText
      });
      alert(message);
    }
  };

  // استخدام دوال البحث المشتركة
  function advancedSearch(employee, searchText, department, job) {
    return window.SharedUtils.advancedEmployeeSearch(employee, searchText, department, job);
  }

  // استخدام دالة تحديث الفلاتر المشتركة
  function updateFilters(employees) {
    window.SharedUtils.updateFilterOptions(employees, 'departmentFilter', 'jobFilter');
  }

  // دالة لعرض النتائج مع دعم الصفحات
  function displayResults(employees) {
    filteredEmployees = employees; // تحديث النتائج المفلترة
    totalEmployees = employees.length; // تحديث العدد الإجمالي

    // حساب البيانات للصفحة الحالية
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const currentPageEmployees = employees.slice(startIndex, endIndex);

    // مسح الجدول
    tableBody.innerHTML = '';

    // عرض موظفي الصفحة الحالية فقط
    currentPageEmployees.forEach(emp => {
      const row = document.createElement("tr");
      const displayValue = (value) => value === null ? '-' : value;

      // إنشاء أزرار الإجراءات بناءً على الصلاحيات
      let actionsHTML = '';

      // زر العرض متاح دائماً
      actionsHTML += `<button class="view-btn" data-id="${emp.code}">عرض</button>`;

      // زر التعديل حسب الصلاحية
      if (hasPermission('can_edit')) {
        actionsHTML += `<button class="edit-btn" data-id="${emp.code}">تعديل</button>`;
      }

      // زر الحذف حسب الصلاحية
      if (hasPermission('can_delete')) {
        actionsHTML += `<button class="delete-btn" data-id="${emp.code}">حذف</button>`;
      }

      row.innerHTML = `<td>${displayValue(emp.code)}</td>
<td>${displayValue(emp.full_name)}</td>
<td>${displayValue(emp.department)}</td>
<td>${displayValue(emp.job_title)}</td>
<td>${emp.hire_date ? formatDate(emp.hire_date) : '-'}</td>
<td>
  ${actionsHTML}
</td>`;
      tableBody.appendChild(row);
    });

    // تحديث عناصر التحكم في الصفحات
    updatePaginationControls();
  }

  // دالة تطبيق البحث
  function applySearch() {
    const searchText = document.getElementById('searchInput').value.trim();
    const department = document.getElementById('departmentFilter').value;
    const job = document.getElementById('jobFilter').value;

    const filteredEmployees = allEmployees.filter(emp =>
      advancedSearch(emp, searchText, department, job)
    );

    // إعادة تعيين الصفحة إلى الأولى عند البحث
    currentPage = 1;
    displayResults(filteredEmployees);
  }

  // دوال التحكم في الصفحات
  function updatePaginationControls() {
    const totalPages = Math.ceil(totalEmployees / itemsPerPage);
    const paginationContainer = document.getElementById('paginationContainer');

    if (!paginationContainer) return;

    // إخفاء التحكم في الصفحات إذا كان هناك صفحة واحدة فقط أو لا توجد بيانات
    if (totalPages <= 1) {
      paginationContainer.style.display = 'none';
      return;
    }

    paginationContainer.style.display = 'flex';

    // تحديث معلومات الصفحة
    const pageInfo = document.getElementById('pageInfo');
    if (pageInfo) {
      const startItem = (currentPage - 1) * itemsPerPage + 1;
      const endItem = Math.min(currentPage * itemsPerPage, totalEmployees);
      pageInfo.textContent = `عرض ${startItem} - ${endItem} من أصل ${totalEmployees} موظف`;
    }

    // تحديث أزرار التنقل
    const prevBtn = document.getElementById('prevPage');
    const nextBtn = document.getElementById('nextPage');

    if (prevBtn) prevBtn.disabled = currentPage <= 1;
    if (nextBtn) nextBtn.disabled = currentPage >= totalPages;

    // تحديث أرقام الصفحات
    updatePageNumbers(totalPages);
  }

  function updatePageNumbers(totalPages) {
    const pageNumbersContainer = document.getElementById('pageNumbers');
    if (!pageNumbersContainer) return;

    pageNumbersContainer.innerHTML = '';

    // حساب نطاق الصفحات المعروضة
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, currentPage + 2);

    // التأكد من عرض 5 صفحات على الأقل إذا كان ذلك ممكناً
    if (endPage - startPage < 4) {
      if (startPage === 1) {
        endPage = Math.min(totalPages, startPage + 4);
      } else if (endPage === totalPages) {
        startPage = Math.max(1, endPage - 4);
      }
    }

    // إضافة الصفحة الأولى إذا لم تكن في النطاق
    if (startPage > 1) {
      addPageButton(1);
      if (startPage > 2) {
        const dots = document.createElement('span');
        dots.textContent = '...';
        dots.className = 'pagination-dots';
        pageNumbersContainer.appendChild(dots);
      }
    }

    // إضافة أرقام الصفحات في النطاق
    for (let i = startPage; i <= endPage; i++) {
      addPageButton(i);
    }

    // إضافة الصفحة الأخيرة إذا لم تكن في النطاق
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        const dots = document.createElement('span');
        dots.textContent = '...';
        dots.className = 'pagination-dots';
        pageNumbersContainer.appendChild(dots);
      }
      addPageButton(totalPages);
    }
  }

  function addPageButton(pageNumber) {
    const pageNumbersContainer = document.getElementById('pageNumbers');
    const button = document.createElement('button');
    button.textContent = pageNumber;
    button.className = 'pagination-btn page-btn';
    if (pageNumber === currentPage) {
      button.classList.add('active');
    }
    button.addEventListener('click', () => goToPage(pageNumber));
    pageNumbersContainer.appendChild(button);
  }

  function goToPage(page) {
    const totalPages = Math.ceil(totalEmployees / itemsPerPage);
    if (page >= 1 && page <= totalPages) {
      currentPage = page;
      displayResults(filteredEmployees);
    }
  }

  function changePage(direction) {
    const newPage = currentPage + direction;
    goToPage(newPage);
  }

  // زر حذف جميع الموظفين
  const deleteAllBtn = document.getElementById("deleteAllBtn");
  deleteAllBtn.addEventListener("click", () => {
    if (confirm("هل أنت متأكد من حذف جميع الموظفين؟ هذا الإجراء لا يمكن التراجع عنه.")) {
      const token = localStorage.getItem('token');
      fetch(`${API_URL}/employees`, {
        method: "DELETE",
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
        .then(res => {
          if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`);
          return res.json();
        })
        .then(() => {
          alert("تم حذف جميع الموظفين بنجاح");
          tableBody.innerHTML = "";
          allEmployees = [];
          updateFilters([]);
        })
        .catch(error => handleApiError(error, "فشل في حذف جميع الموظفين"));
    }
  });

  // استخدام دالة تحميل الموظفين من shared-utils.js
  async function loadEmployees() {
    if (!serverRunning) {
      return;
    }

    try {
      if (window.SharedUtils && window.SharedUtils.loadEmployees) {
        const data = await window.SharedUtils.loadEmployees(false);
        allEmployees = data.sort((a, b) => {
          const codeA = parseInt(a.code) || 0;
          const codeB = parseInt(b.code) || 0;
          return codeA - codeB;
        });
      } else {
        // fallback للكود القديم
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/employees`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        const data = await response.json();


        allEmployees = data.sort((a, b) => {
          const codeA = parseInt(a.code) || 0;
          const codeB = parseInt(b.code) || 0;
          return codeA - codeB;
        });
      }

      updateFilters(allEmployees);
      displayResults(allEmployees);
    } catch (error) {
      handleApiError(error, "فشل في تحميل بيانات الموظفين");
    }
  }

  // أزرار عرض وتعديل وحذف
  tableBody.addEventListener("click", function(e) {
    if (e.target.classList.contains("view-btn")) {
      const code = e.target.dataset.id;
      window.open(`view.html?code=${code}`, '_blank');
    } else if (e.target.classList.contains("edit-btn")) {
      const code = e.target.dataset.id;
      window.location.href = `edit.html?code=${code}`;
    } else if (e.target.classList.contains("delete-btn")) {
      if (confirm("هل أنت متأكد من حذف الموظف؟")) {
        const code = e.target.dataset.id;
        const token = localStorage.getItem('token');
        fetch(`${API_URL}/employees/${code}`, {
          method: "DELETE",
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
          .then(res => {
            if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`);
            return res.json();
          })
          .then(() => {
            // تحديث قائمة الموظفين بعد الحذف
            allEmployees = allEmployees.filter(emp => emp.code !== code);

            // التحقق من الحاجة لتعديل الصفحة الحالية
            const totalPages = Math.ceil(allEmployees.length / itemsPerPage);
            if (currentPage > totalPages && totalPages > 0) {
              currentPage = totalPages;
            }

            updateFilters(allEmployees);
            displayResults(filteredEmployees.filter(emp => emp.code !== code));
          })
          .catch(error => handleApiError(error, "فشل في حذف الموظف"));
      }
    }
  });

  // إضافة مستمعي الأحداث
  document.getElementById('searchBtn').addEventListener('click', applySearch);
  document.getElementById('resetSearchBtn').addEventListener('click', () => {
    document.getElementById('searchInput').value = '';
    document.getElementById('departmentFilter').value = '';
    document.getElementById('jobFilter').value = '';
    // إعادة تعيين الصفحة إلى الأولى عند إعادة تعيين البحث
    currentPage = 1;
    displayResults(allEmployees);
  });

  // البحث عند الضغط على Enter
  document.getElementById('searchInput').addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
      applySearch();
    }
  });

  // البحث عند تغيير القوائم المنسدلة
  document.getElementById('departmentFilter').addEventListener('change', applySearch);
  document.getElementById('jobFilter').addEventListener('change', applySearch);

  // Export functionality
  const exportBtn = document.getElementById('exportBtn');
  const exportModal = document.getElementById('exportModal');
  const closeBtn = exportModal.querySelector('.close');
  const cancelBtn = exportModal.querySelector('.cancel-btn');
  const confirmExportBtn = document.getElementById('confirmExport');
  const selectAllFields = document.getElementById('selectAllFields');
  const fieldsGrid = document.getElementById('fieldsGrid');

  // Field definitions with Arabic labels
  const fieldDefinitions = [
    { key: 'code', label: 'الرقم الوظيفي' },
    { key: 'full_name', label: 'الاسم الكامل' },
    { key: 'department', label: 'الإدارة' },
    { key: 'job_title', label: 'الوظيفة' },
    { key: 'hire_date', label: 'تاريخ التعيين' },
    { key: 'address', label: 'العنوان' },
    { key: 'qualification', label: 'المؤهل' },
    { key: 'phone', label: 'التليفون' },
    { key: 'birth_date', label: 'تاريخ الميلاد' },
    { key: 'marital_status', label: 'الحالة الاجتماعية' },
    { key: 'children', label: 'عدد الأبناء' },
    { key: 'national_id', label: 'الرقم القومي' },
    { key: 'social_insurance', label: 'التأمين التكافلي' },
    { key: 'insurance_number', label: 'الرقم التأميني' },
    { key: 'insurance_entity', label: 'جهة التأمين' },
    { key: 'insurance_start', label: 'تاريخ التأمين عليه' },
    { key: 'insurance_job', label: 'المهنة في التأمينات' },
    { key: 'insurance_salary', label: 'راتب التأمينات' },
    { key: 'worker_cost', label: 'ما يتحمله العامل' },
    { key: 'company_cost', label: 'ما تتحمله الشركة' },
    { key: 'total_salary', label: 'الأجر الشامل' },
    { key: 'health_card', label: 'رقم البطاقة الصحية' },
    { key: 'skill_level', label: 'قياس المهارة' },
    { key: 'skill_start', label: 'تاريخ بداية قياس المهارة' },
    { key: 'skill_end', label: 'تاريخ انتهاء قياس المهارة' },
    { key: 'skill_remaining', label: 'الوقت المتبقى على انتهاء قياس المهارة' },
    { key: 'skill_job', label: 'مهنة قياس المهارة' },
    { key: 'leave_balance', label: 'رصيد الإجازات' },
    { key: 'leave_used', label: 'الإجازات المستخدمة' },
    { key: 'leave_remaining', label: 'الإجازات المتبقية' },
    { key: 'special_needs', label: 'ذوي الهمم' }
  ];

  // Initialize field checkboxes
  function initializeFieldCheckboxes() {
    fieldsGrid.innerHTML = '';
    fieldDefinitions.forEach(field => {
      const div = document.createElement('div');
      div.className = 'field-checkbox';
      div.innerHTML = `
        <input type="checkbox" id="${field.key}" value="${field.key}" checked>
        <label for="${field.key}">${field.label}</label>
      `;
      fieldsGrid.appendChild(div);
    });
  }

  // Show modal
  exportBtn.addEventListener('click', () => {
    exportModal.style.display = 'block';
    initializeFieldCheckboxes();
  });

  // Close modal
  function closeModal() {
    exportModal.style.display = 'none';
  }

  closeBtn.addEventListener('click', closeModal);
  cancelBtn.addEventListener('click', closeModal);

  // Close modal when clicking outside
  window.addEventListener('click', (e) => {
    if (e.target === exportModal) {
      closeModal();
    }
  });

  // Select all fields
  selectAllFields.addEventListener('change', (e) => {
    const checkboxes = fieldsGrid.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
      checkbox.checked = e.target.checked;
    });
  });

  // Export to Excel
  confirmExportBtn.addEventListener('click', () => {
    const selectedFields = Array.from(fieldsGrid.querySelectorAll('input[type="checkbox"]:checked'))
      .map(checkbox => checkbox.value);

    if (selectedFields.length === 0) {
      alert('الرجاء اختيار حقل واحد على الأقل');
      return;
    }

    // استخدام النتائج المفلترة إذا كانت موجودة، وإلا استخدام كل الموظفين
    const employeesToExport = filteredEmployees && filteredEmployees.length > 0 ? filteredEmployees : allEmployees;

    // تجهيز البيانات للتصدير
    const headers = selectedFields.map(field => 
      fieldDefinitions.find(f => f.key === field).label
    );

    const data = employeesToExport.map(emp => 
      selectedFields.map(field => emp[field] || '')
    );

    // إضافة BOM لدعم اللغة العربية
    const BOM = '\uFEFF';
    let csvContent = BOM + headers.join(';') + '\n';
    
    data.forEach(row => {
      csvContent += row.map(cell => 
        cell !== undefined && cell !== null ? 
        `"${String(cell).replace(/"/g, '""')}"` : 
        '""'
      ).join(';') + '\n';
    });

    // إنشاء وتنزيل الملف
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'بيانات_الموظفين.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    closeModal();
  });

  // ربط الدوال العامة بالدوال المحلية
  globalChangePage = changePage;
  globalGoToPage = goToPage;
});

// دوال عامة للتحكم في الصفحات (يمكن استدعاؤها من HTML)
let globalChangePage, globalGoToPage;

// تم ربط الدوال العامة بالدوال المحلية داخل الدالة الرئيسية

window.changePage = function(direction) {
  if (globalChangePage) {
    globalChangePage(direction);
  }
};

window.goToPage = function(page) {
  if (globalGoToPage) {
    globalGoToPage(page);
  }
};
