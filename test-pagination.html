<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>اختبار نظام الصفحات</title>
  <link rel="stylesheet" href="shared-styles.css">
  <link rel="stylesheet" href="index-table.css">
  <style>
    body {
      padding: 20px;
      background: #f5f5f5;
    }
    .test-container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .test-info {
      background: #e3f2fd;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
      border-left: 4px solid #2196f3;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1>اختبار نظام الصفحات لجدول الموظفين</h1>
    
    <div class="test-info">
      <h3>معلومات الاختبار:</h3>
      <ul>
        <li>عدد العناصر لكل صفحة: 50</li>
        <li>يتم عرض أرقام الصفحات مع التنقل</li>
        <li>أزرار السابق والتالي</li>
        <li>معلومات الصفحة الحالية</li>
        <li>التصميم متجاوب للشاشات الصغيرة</li>
      </ul>
    </div>

    <!-- محاكاة جدول الموظفين -->
    <div class="employee-table-container">
      <table class="employee-table">
        <thead>
          <tr>
            <th>الرقم الوظيفي</th>
            <th>الاسم</th>
            <th>الإدارة</th>
            <th>الوظيفة</th>
            <th>تاريخ التعيين</th>
            <th>الإجراءات</th>
          </tr>
        </thead>
        <tbody id="testTableBody">
          <!-- سيتم ملء البيانات التجريبية هنا -->
        </tbody>
      </table>
      
      <!-- نظام التحكم في الصفحات -->
      <div class="pagination-container" id="paginationContainer">
        <button class="pagination-btn prev-btn" id="prevPage" onclick="changePage(-1)">
          <i class="fas fa-chevron-right"></i> السابق
        </button>
        
        <div class="page-numbers" id="pageNumbers">
          <!-- أرقام الصفحات ستضاف ديناميكياً -->
        </div>
        
        <button class="pagination-btn next-btn" id="nextPage" onclick="changePage(1)">
          التالي <i class="fas fa-chevron-left"></i>
        </button>
        
        <div class="page-info" id="pageInfo">
          <!-- معلومات الصفحة ستضاف ديناميكياً -->
        </div>
      </div>
    </div>
  </div>

  <script>
    // بيانات تجريبية لاختبار النظام
    let testEmployees = [];
    let currentPage = 1;
    const itemsPerPage = 50;
    let totalEmployees = 0;

    // إنشاء بيانات تجريبية
    function generateTestData() {
      const departments = ['الموارد البشرية', 'المحاسبة', 'التسويق', 'تقنية المعلومات', 'المبيعات'];
      const jobs = ['مدير', 'محاسب', 'مطور', 'مسوق', 'مندوب مبيعات'];
      
      for (let i = 1; i <= 127; i++) {
        testEmployees.push({
          code: i,
          full_name: `موظف تجريبي ${i}`,
          department: departments[Math.floor(Math.random() * departments.length)],
          job_title: jobs[Math.floor(Math.random() * jobs.length)],
          hire_date: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`
        });
      }
    }

    // عرض البيانات مع الصفحات
    function displayResults(employees) {
      totalEmployees = employees.length;
      
      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      const currentPageEmployees = employees.slice(startIndex, endIndex);
      
      const tableBody = document.getElementById('testTableBody');
      tableBody.innerHTML = '';
      
      currentPageEmployees.forEach(emp => {
        const row = document.createElement("tr");
        row.innerHTML = `
          <td>${emp.code}</td>
          <td>${emp.full_name}</td>
          <td>${emp.department}</td>
          <td>${emp.job_title}</td>
          <td>${emp.hire_date}</td>
          <td>
            <button class="view-btn">عرض</button>
            <button class="edit-btn">تعديل</button>
            <button class="delete-btn">حذف</button>
          </td>
        `;
        tableBody.appendChild(row);
      });
      
      updatePaginationControls();
    }

    // تحديث عناصر التحكم في الصفحات
    function updatePaginationControls() {
      const totalPages = Math.ceil(totalEmployees / itemsPerPage);
      const paginationContainer = document.getElementById('paginationContainer');
      
      if (totalPages <= 1) {
        paginationContainer.style.display = 'none';
        return;
      }
      
      paginationContainer.style.display = 'flex';
      
      // تحديث معلومات الصفحة
      const pageInfo = document.getElementById('pageInfo');
      const startItem = (currentPage - 1) * itemsPerPage + 1;
      const endItem = Math.min(currentPage * itemsPerPage, totalEmployees);
      pageInfo.textContent = `عرض ${startItem} - ${endItem} من أصل ${totalEmployees} موظف`;
      
      // تحديث أزرار التنقل
      const prevBtn = document.getElementById('prevPage');
      const nextBtn = document.getElementById('nextPage');
      
      prevBtn.disabled = currentPage <= 1;
      nextBtn.disabled = currentPage >= totalPages;
      
      // تحديث أرقام الصفحات
      updatePageNumbers(totalPages);
    }

    // تحديث أرقام الصفحات
    function updatePageNumbers(totalPages) {
      const pageNumbersContainer = document.getElementById('pageNumbers');
      pageNumbersContainer.innerHTML = '';
      
      let startPage = Math.max(1, currentPage - 2);
      let endPage = Math.min(totalPages, currentPage + 2);
      
      if (endPage - startPage < 4) {
        if (startPage === 1) {
          endPage = Math.min(totalPages, startPage + 4);
        } else if (endPage === totalPages) {
          startPage = Math.max(1, endPage - 4);
        }
      }
      
      // إضافة الصفحة الأولى
      if (startPage > 1) {
        addPageButton(1);
        if (startPage > 2) {
          const dots = document.createElement('span');
          dots.textContent = '...';
          dots.className = 'pagination-dots';
          pageNumbersContainer.appendChild(dots);
        }
      }
      
      // إضافة أرقام الصفحات في النطاق
      for (let i = startPage; i <= endPage; i++) {
        addPageButton(i);
      }
      
      // إضافة الصفحة الأخيرة
      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          const dots = document.createElement('span');
          dots.textContent = '...';
          dots.className = 'pagination-dots';
          pageNumbersContainer.appendChild(dots);
        }
        addPageButton(totalPages);
      }
    }

    // إضافة زر صفحة
    function addPageButton(pageNumber) {
      const pageNumbersContainer = document.getElementById('pageNumbers');
      const button = document.createElement('button');
      button.textContent = pageNumber;
      button.className = 'pagination-btn page-btn';
      if (pageNumber === currentPage) {
        button.classList.add('active');
      }
      button.addEventListener('click', () => goToPage(pageNumber));
      pageNumbersContainer.appendChild(button);
    }

    // الانتقال لصفحة محددة
    function goToPage(page) {
      const totalPages = Math.ceil(totalEmployees / itemsPerPage);
      if (page >= 1 && page <= totalPages) {
        currentPage = page;
        displayResults(testEmployees);
      }
    }

    // تغيير الصفحة
    function changePage(direction) {
      const newPage = currentPage + direction;
      goToPage(newPage);
    }

    // تهيئة الاختبار
    document.addEventListener('DOMContentLoaded', function() {
      generateTestData();
      displayResults(testEmployees);
      
      console.log('تم إنشاء', testEmployees.length, 'موظف تجريبي');
      console.log('عدد الصفحات المتوقع:', Math.ceil(testEmployees.length / itemsPerPage));
    });
  </script>
</body>
</html>
