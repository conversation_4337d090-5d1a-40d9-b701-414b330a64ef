<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>قاعدة بيانات الموظفين</title>

  <link rel="stylesheet" href="shared-styles.css">
  <link rel="stylesheet" href="style.css" />
  <link rel="stylesheet" href="index-table.css">
  <link rel="stylesheet" href="no-sidebar-layout.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">



  <script src="permissions.js" defer></script>
</head>
<body class="index-page">

  <!-- زر العودة للوحة التحكم -->
  <div class="back-to-dashboard">
    <a href="dashboard.html" class="dashboard-btn">
      <i class="fas fa-home"></i>
      <span>العودة للوحة التحكم</span>
    </a>
  </div>


  <!-- المحتوى الرئيسي -->
  <div class="main-content full-width" id="mainContent">
    <h1>قاعدة بيانات الموظفين</h1>

    <div class="server-controls">
      <button id="serverStatus" class="status-btn">حالة السيرفر: غير متصل</button>
      <button id="toggleServer" class="control-btn">تشغيل السيرفر</button>

    </div>

    <div class="search-container">
      <div class="search-filters">
        <input type="text" id="searchInput" placeholder="ابحث عن موظف...">
        <select id="departmentFilter">
          <option value="">كل الإدارات</option>
        </select>
        <select id="jobFilter">
          <option value="">كل الوظائف</option>
        </select>
        <button id="searchBtn" class="search-btn">بحث</button>
        <button id="resetSearchBtn" class="reset-btn">إعادة تعيين</button>
      </div>
    </div>

    <div class="actions-bar">
      <button id="deleteAllBtn" class="delete-all" data-permission="delete_all_employees">حذف جميع الموظفين</button>
    </div>

    <div class="employee-table-container">
      <div class="table-controls">
        <button id="exportBtn" class="export-btn">تصدير إلى Excel</button>
      </div>
      <table class="employee-table">
        <thead>
          <tr>
            <th>الرقم الوظيفي</th>
            <th>الاسم</th>
            <th>الإدارة</th>
            <th>الوظيفة</th>
            <th>تاريخ التعيين</th>
            <th>الإجراءات</th>
          </tr>
        </thead>
        <tbody id="employeeTableBody"></tbody>
      </table>

      <!-- نظام التحكم في الصفحات -->
      <div class="pagination-container" id="paginationContainer" style="display: none;">
        <button class="pagination-btn prev-btn" id="prevPage" onclick="changePage(-1)">
          <i class="fas fa-chevron-right"></i> السابق
        </button>

        <div class="page-numbers" id="pageNumbers">
          <!-- أرقام الصفحات ستضاف ديناميكياً -->
        </div>

        <button class="pagination-btn next-btn" id="nextPage" onclick="changePage(1)">
          التالي <i class="fas fa-chevron-left"></i>
        </button>

        <div class="page-info" id="pageInfo">
          <!-- معلومات الصفحة ستضاف ديناميكياً -->
        </div>
      </div>
    </div>
  </div>

  <!-- Modal for field selection -->
  <div id="exportModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>اختر الحقول للتصدير</h2>
        <span class="close">&times;</span>
      </div>
      <div class="modal-body">
        <div class="field-selection">
          <div class="select-all">
            <input type="checkbox" id="selectAllFields">
            <label for="selectAllFields">تحديد الكل</label>
          </div>
          <div class="fields-grid" id="fieldsGrid">
            <!-- Fields will be added dynamically -->
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button id="confirmExport" class="confirm-btn">تصدير</button>
        <button class="cancel-btn">إلغاء</button>
      </div>
    </div>
  </div>

  <script src="config.js"></script>
  <script src="shared-utils.js"></script>
  <script src="dateUtils.js"></script>
  <script src="arabic-date-picker.js"></script>
  <script src="permissions.js"></script>
  <script src="main.js"></script>
  <script src="script.js"></script>

</body>
</html>
