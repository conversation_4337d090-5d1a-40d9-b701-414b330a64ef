/* ===== تنسيقات نظام الصفحات المشتركة ===== */

.pagination-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-top: 25px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  flex-wrap: wrap;
}

.pagination-btn {
  padding: 10px 15px;
  border: 2px solid var(--primary-color);
  background: var(--white);
  color: var(--primary-color);
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  min-width: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.pagination-btn:hover:not(:disabled) {
  background: var(--primary-color);
  color: #ffffff !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.pagination-btn.active {
  background: var(--primary-color);
  color: #ffffff !important;
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.page-btn {
  min-width: 40px;
  padding: 8px 12px;
}

.pagination-dots {
  color: var(--text-secondary);
  font-weight: bold;
  padding: 0 5px;
  font-size: 16px;
}

.page-info {
  background: var(--white);
  padding: 8px 15px;
  border-radius: 6px;
  color: var(--text-secondary);
  font-size: 13px;
  font-weight: 500;
  border: 1px solid var(--border-color);
  white-space: nowrap;
}

.prev-btn, .next-btn {
  font-weight: 700;
  padding: 10px 18px;
  background: linear-gradient(135deg, var(--primary-color) 0%, #1976d2 100%);
  color: #ffffff !important;
  border: none;
}

.prev-btn:hover:not(:disabled), 
.next-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  color: #ffffff !important;
  transform: translateY(-2px);
}

/* التصميم المتجاوب للصفحات */
@media (max-width: 768px) {
  .pagination-container {
    gap: 10px;
    padding: 15px;
    flex-direction: column;
  }
  
  .page-numbers {
    order: 1;
    justify-content: center;
  }
  
  .prev-btn, .next-btn {
    order: 2;
    min-width: 100px;
  }
  
  .page-info {
    order: 3;
    text-align: center;
    width: 100%;
  }
  
  .pagination-btn {
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .page-btn {
    min-width: 35px;
    padding: 6px 10px;
  }
}

@media (max-width: 480px) {
  .pagination-container {
    gap: 8px;
    padding: 12px;
  }
  
  .page-numbers {
    gap: 5px;
  }
  
  .pagination-btn {
    padding: 6px 10px;
    font-size: 12px;
    min-width: 35px;
  }
  
  .page-btn {
    min-width: 30px;
    padding: 5px 8px;
  }
  
  .prev-btn, .next-btn {
    padding: 8px 12px;
    min-width: 80px;
  }
  
  .page-info {
    font-size: 12px;
    padding: 6px 12px;
  }
}

/* تحسينات إضافية للمظهر */
.pagination-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0.3;
}

.pagination-container {
  position: relative;
}

/* تأثيرات التحويم المحسنة */
.pagination-btn:not(:disabled):not(.active):hover {
  background: linear-gradient(135deg, var(--primary-color) 0%, #1976d2 100%);
  color: #ffffff !important;
  border-color: #1976d2;
}

.page-btn.active {
  background: linear-gradient(135deg, var(--primary-color) 0%, #1976d2 100%);
  color: #ffffff !important;
  border-color: #1976d2;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);
}

/* تنسيقات خاصة للجداول المختلفة لتجنب التعارض */
.vacations-table-container .pagination-container,
.extra-hours-table-container .pagination-container,
.report-table-container .pagination-container {
  margin-top: 20px;
}

/* تأكد من أن المتغيرات متوفرة */
:root {
  --primary-color: #2196f3;
  --white: #ffffff;
  --text-secondary: #666;
  --border-color: #ddd;
}
